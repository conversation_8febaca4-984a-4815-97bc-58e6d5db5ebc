<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .tab-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .rule-card {
            transition: all 0.3s ease;
        }

        .rule-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            border-left: 3px solid #e5e7eb;
            padding-left: 1rem;
        }

        .form-section.active {
            border-left-color: #6366f1;
        }

        .modal-content {
            max-height: 90vh;
            overflow-y: auto;
        }

        .required-field::after {
            content: " *";
            color: #ef4444;
        }

        .param-level-1 {
            border-left: 2px solid #e5e7eb;
            background-color: #f9fafb;
        }

        .param-level-2 {
            border-left: 2px solid #d1d5db;
            background-color: #f3f4f6;
        }

        .param-level-3 {
            border-left: 2px solid #9ca3af;
            background-color: #f3f4f6;
        }

        .children-container {
            margin-top: 0.5rem;
        }

        .add-child-btn {
            transition: all 0.2s ease;
        }

        .add-child-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body class="bg-gray-50">
<!-- 顶部导航栏 -->
<nav class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <img class="h-8 w-8"
                     src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center"
                     alt="Logo">
                <span class="ml-3 text-xl font-bold text-gray-800">企业API开放平台</span>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-3">
                    <!--                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">-->
                    <span class="text-sm font-medium text-gray-700">系统管理员</span>
                </div>
            </div>
        </div>
    </div>
</nav>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">系统管理</h1>
        <p class="mt-2 text-gray-600">管理限流规则、系统配置和平台设置</p>
    </div>

    <!-- 标签页导航 -->
    <div class="mb-8">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button onclick="switchTab('rate-limit')" id="rate-limit-tab"
                        class="tab-active py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                    <i class="fas fa-tachometer-alt mr-2"></i>限流配置
                </button>
                <button onclick="switchTab('api-management')" id="api-management-tab"
                        class="text-gray-500 hover:text-gray-700 py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                    <i class="fas fa-cogs mr-2"></i>API管理
                </button>
                <button onclick="switchTab('user-management')" id="user-management-tab"
                        class="text-gray-500 hover:text-gray-700 py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                    <i class="fas fa-users mr-2"></i>用户管理
                </button>
                <!--<button onclick="switchTab('system-config')" id="system-config-tab"
                        class="text-gray-500 hover:text-gray-700 py-2 px-1 border-b-2 border-transparent font-medium text-sm whitespace-nowrap">
                    <i class="fas fa-server mr-2"></i>系统配置
                </button>-->
            </nav>
        </div>
    </div>

    <!-- 限流配置标签页 -->
    <div id="rate-limit-content" class="tab-content">


        <!-- API限流配置 -->
        <div class="bg-white rounded-lg shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">API限流配置</h3>
                    <div class="text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i>修改后自动保存
                    </div>
                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <select id="businessSystemFilter" onchange="filterApis()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">全部业务系统</option>
                            <option value="user_management">用户管理系统</option>
                            <option value="order_management">订单管理系统</option>
                            <option value="inventory_management">库存管理系统</option>
                            <option value="finance_management">财务管理系统</option>
                        </select>
                    </div>
                    <div>
                        <input type="text" id="apiSearchInput" placeholder="搜索API名称..." onkeyup="filterApis()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <button onclick="resetFilters()" class="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50">
                            <i class="fas fa-undo mr-2"></i>重置筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- API限流配置列表 -->
            <div id="apiRateLimitList">
                <!-- 用户管理系统 -->
                <div class="business-system-group" data-system="user_management">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-users text-blue-500 text-lg mr-3"></i>
                                <h4 class="text-md font-semibold text-gray-900">用户管理系统</h4>
                            </div>
                            <button onclick="toggleSystemGroup('user_management')" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-chevron-down transform transition-transform" id="user_management_icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="user_management_apis" class="api-group">
                        <!-- 用户信息查询API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="用户信息查询">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">用户信息查询</h5>
                                    <p class="text-xs text-gray-500 mt-1">GET /v1/user/info</p>
                                    <p class="text-xs text-gray-400 mt-1">查询用户基本信息</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('user_info', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="100" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('user_info', this)">
                                </div>
                            </div>
                        </div>

                        <!-- 用户创建API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="用户创建">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">用户创建</h5>
                                    <p class="text-xs text-gray-500 mt-1">POST /v1/user/create</p>
                                    <p class="text-xs text-gray-400 mt-1">创建新用户账户</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('user_create', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="50" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('user_create', this)">
                                </div>
                            </div>
                        </div>

                        <!-- 用户更新API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="用户更新">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">用户更新</h5>
                                    <p class="text-xs text-gray-500 mt-1">PUT /v1/user/update</p>
                                    <p class="text-xs text-gray-400 mt-1">更新用户信息</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('user_update', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="30" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('user_update', this)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单管理系统 -->
                <div class="business-system-group" data-system="order_management">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-shopping-cart text-green-500 text-lg mr-3"></i>
                                <h4 class="text-md font-semibold text-gray-900">订单管理系统</h4>
                            </div>
                            <button onclick="toggleSystemGroup('order_management')" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-chevron-down transform transition-transform" id="order_management_icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="order_management_apis" class="api-group" style="display: none;">
                        <!-- 订单查询API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="订单查询">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">订单查询</h5>
                                    <p class="text-xs text-gray-500 mt-1">GET /v1/order/query</p>
                                    <p class="text-xs text-gray-400 mt-1">查询订单详细信息</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('order_query', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="200" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('order_query', this)">
                                </div>
                            </div>
                        </div>

                        <!-- 订单创建API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="订单创建">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">订单创建</h5>
                                    <p class="text-xs text-gray-500 mt-1">POST /v1/order/create</p>
                                    <p class="text-xs text-gray-400 mt-1">创建新订单</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('order_create', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="100" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('order_create', this)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 库存管理系统 -->
                <div class="business-system-group" data-system="inventory_management">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-warehouse text-purple-500 text-lg mr-3"></i>
                                <h4 class="text-md font-semibold text-gray-900">库存管理系统</h4>
                            </div>
                            <button onclick="toggleSystemGroup('inventory_management')" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-chevron-down transform transition-transform" id="inventory_management_icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="inventory_management_apis" class="api-group" style="display: none;">
                        <!-- 库存查询API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="库存查询">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">库存查询</h5>
                                    <p class="text-xs text-gray-500 mt-1">GET /v1/inventory/query</p>
                                    <p class="text-xs text-gray-400 mt-1">查询商品库存信息</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('inventory_query', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="300" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('inventory_query', this)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务管理系统 -->
                <div class="business-system-group" data-system="finance_management">
                    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-chart-bar text-orange-500 text-lg mr-3"></i>
                                <h4 class="text-md font-semibold text-gray-900">财务管理系统</h4>
                            </div>
                            <button onclick="toggleSystemGroup('finance_management')" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-chevron-down transform transition-transform" id="finance_management_icon"></i>
                            </button>
                        </div>
                    </div>
                    <div id="finance_management_apis" class="api-group" style="display: none;">
                        <!-- 账单查询API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="账单查询">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">账单查询</h5>
                                    <p class="text-xs text-gray-500 mt-1">GET /v1/finance/bill/query</p>
                                    <p class="text-xs text-gray-400 mt-1">查询用户账单信息</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('bill_query', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="150" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('bill_query', this)">
                                </div>
                            </div>
                        </div>

                        <!-- 支付处理API -->
                        <div class="px-6 py-4 border-b border-gray-100 api-item" data-api-name="支付处理">
                            <div class="grid grid-cols-1 lg:grid-cols-8 gap-4 items-center">
                                <div class="lg:col-span-4">
                                    <h5 class="text-sm font-medium text-gray-900">支付处理</h5>
                                    <p class="text-xs text-gray-500 mt-1">POST /v1/finance/payment/process</p>
                                    <p class="text-xs text-gray-400 mt-1">处理支付请求</p>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流类型</label>
                                    <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                            onchange="autoSaveApiRateLimit('payment_process', this)">
                                        <option value="second">按秒</option>
                                        <option value="minute" selected>按分钟</option>
                                        <option value="hour">按小时</option>
                                        <option value="day">按天</option>
                                    </select>
                                </div>
                                <div class="lg:col-span-2">
                                    <label class="block text-xs font-medium text-gray-600 mb-1">限流次数</label>
                                    <input type="number" value="50" min="1" class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                           onchange="autoSaveApiRateLimit('payment_process', this)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </div>

    <!-- API管理标签页 -->
    <div id="api-management-content" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">API接口管理</h3>
                    <button onclick="openApiModal('create')"
                            class="gradient-bg text-white px-4 py-2 rounded-lg font-medium hover:opacity-90">
                        <i class="fas fa-plus mr-2"></i>注册新API
                    </button>

                </div>
            </div>

            <!-- 搜索和筛选 -->
            <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <select id="businessSystemFilter" onchange="filterApisBySystem()" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option value="">全部业务系统</option>
                            <option value="用户管理系统">用户管理系统</option>
                            <option value="订单管理系统">订单管理系统</option>
                            <option value="库存管理系统">库存管理系统</option>
                            <option value="财务管理系统">财务管理系统</option>
                            <option value="AI服务类">AI服务类</option>
                        </select>
                    </div>
                    <div>
                        <input type="text" id="apiNameFilter" placeholder="搜索API名称..." onkeyup="filterApisByName()"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <button onclick="resetApiFilters()" class="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50">
                            <i class="fas fa-undo mr-2"></i>重置筛选
                        </button>
                    </div>
                </div>
            </div>

            <!-- API列表 -->
            <div id="apiList">
                <!-- 用户管理系统 -->
                <div class="api-system-group" data-system="用户管理系统">
                    <div class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center">
                            <i class="fas fa-users text-blue-500 text-lg mr-3"></i>
                            <h4 class="text-md font-semibold text-gray-900">用户管理系统</h4>
                            <span class="ml-3 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">2个API</span>
                        </div>
                    </div>

                    <div class="px-6 py-4 api-item" data-api-name="用户信息查询" data-system="用户管理系统">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="text-md font-medium text-gray-900">用户信息查询</h4>
                                    <span class="ml-3 text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">GET</span>
                                    <span class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">/v1/user/info - 查询用户基本信息</p>
                                <div class="flex items-center mt-2 text-xs text-gray-500">
                                    <span>业务系统: 用户管理系统</span>
                                    <span class="mx-2">•</span>
                                    <span>今日调用: 1,247次</span>
                                    <span class="mx-2">•</span>
                                    <span>平均响应: 145ms</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="openApiModal('edit', 'user-info')"
                                        class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="px-6 py-4 api-item" data-api-name="用户创建" data-system="用户管理系统">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="text-md font-medium text-gray-900">用户创建</h4>
                                    <span class="ml-3 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">POST</span>
                                    <span class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">/v1/user/create - 创建新用户账户</p>
                                <div class="flex items-center mt-2 text-xs text-gray-500">
                                    <span>业务系统: 用户管理系统</span>
                                    <span class="mx-2">•</span>
                                    <span>今日调用: 456次</span>
                                    <span class="mx-2">•</span>
                                    <span>平均响应: 289ms</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="openApiModal('edit', 'user-create')"
                                        class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI服务类 -->
                <div class="api-system-group" data-system="AI服务类">
                    <div class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                        <div class="flex items-center">
                            <i class="fas fa-robot text-red-500 text-lg mr-3"></i>
                            <h4 class="text-md font-semibold text-gray-900">AI服务类</h4>
                            <span class="ml-3 text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">2个API</span>
                        </div>
                    </div>

                    <div class="px-6 py-4 api-item" data-api-name="文本情感分析" data-system="AI服务类">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="text-md font-medium text-gray-900">文本情感分析</h4>
                                    <span class="ml-3 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">POST</span>
                                    <span class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">/v1/nlp/sentiment - 分析文本情感倾向</p>
                                <div class="flex items-center mt-2 text-xs text-gray-500">
                                    <span>业务系统: AI服务类</span>
                                    <span class="mx-2">•</span>
                                    <span>今日调用: 1,247次</span>
                                    <span class="mx-2">•</span>
                                    <span>平均响应: 245ms</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="openApiModal('edit', 'sentiment-analysis')"
                                        class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="px-6 py-4 api-item" data-api-name="图像识别" data-system="AI服务类">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center">
                                    <h4 class="text-md font-medium text-gray-900">图像识别</h4>
                                    <span class="ml-3 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full">POST</span>
                                    <span class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">/v1/vision/recognition - 识别图像中的物体和场景</p>
                                <div class="flex items-center mt-2 text-xs text-gray-500">
                                    <span>业务系统: AI服务类</span>
                                    <span class="mx-2">•</span>
                                    <span>今日调用: 856次</span>
                                    <span class="mx-2">•</span>
                                    <span>平均响应: 1.2s</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="openApiModal('edit', 'image-recognition')"
                                        class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户管理标签页 -->
    <div id="user-management-content" class="tab-content hidden">
        <div class="bg-white rounded-lg shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">用户管理</h3>
                    <button onclick="openUserModal('create')" class="gradient-bg text-white px-4 py-2 rounded-lg font-medium hover:opacity-90">
                        <i class="fas fa-plus mr-2"></i>新增用户
                    </button>
                </div>
            </div>

            <!-- 用户统计 -->
            <!--<div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">10,247</div>
                        <div class="text-sm text-gray-600">总用户数</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">8,956</div>
                        <div class="text-sm text-gray-600">个人用户</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">1,291</div>
                        <div class="text-sm text-gray-600">企业用户</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600">156</div>
                        <div class="text-sm text-gray-600">今日新增</div>
                    </div>
                </div>
            </div>-->

            <!-- 用户列表 -->
            <div class="divide-y divide-gray-200">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">张开发者</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                                <div class="text-xs text-gray-400 mt-1">科技有限公司</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                            <div class="text-sm text-gray-500">
                                <div>今日调用: 1,247次</div>
                                <div>注册时间: 2024-01-15</div>
                            </div>
                            <button onclick="openUserModal('edit', 'zhang')"
                                    class="text-indigo-600 hover:text-indigo-500 text-sm mr-3">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button onclick="openKeyManagement('zhang')"
                                    class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-key mr-1"></i>密钥管理
                            </button>
                        </div>
                    </div>
                </div>

                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">李工程师</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                                <div class="text-xs text-gray-400 mt-1">创新科技公司</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                            <div class="text-sm text-gray-500">
                                <div>今日调用: 863次</div>
                                <div>注册时间: 2024-01-20</div>
                            </div>
                            <button onclick="openUserModal('edit', 'li')"
                                    class="text-indigo-600 hover:text-indigo-500 text-sm mr-3">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button onclick="openKeyManagement('li')"
                                    class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-key mr-1"></i>密钥管理
                            </button>
                        </div>
                    </div>
                </div>

                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">王企业</div>
                                <div class="text-sm text-gray-500"><EMAIL></div>
                                <div class="text-xs text-gray-400 mt-1">智能制造有限公司</div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">正常</span>
                            <div class="text-sm text-gray-500">
                                <div>今日调用: 2,451次</div>
                                <div>注册时间: 2024-02-01</div>
                            </div>
                            <button onclick="openUserModal('edit', 'wang')"
                                    class="text-indigo-600 hover:text-indigo-500 text-sm mr-3">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button onclick="openKeyManagement('wang')"
                                    class="text-indigo-600 hover:text-indigo-500 text-sm">
                                <i class="fas fa-key mr-1"></i>密钥管理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 系统配置标签页 -->
    <div id="system-config-content" class="tab-content hidden">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 基础配置 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">基础配置</h3>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">平台名称</label>
                        <input type="text" value="AI-API开放平台"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">API基础URL</label>
                        <input type="url" value="https://api.ai-platform.com"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">默认配额（次/月）</label>
                        <input type="number" value="10000"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">最大文件大小（MB）</label>
                        <input type="number" value="10"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                </form>
            </div>

            <!-- 安全配置 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">安全配置</h3>
                <div class="space-y-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">强制HTTPS</h4>
                            <p class="text-xs text-gray-500">所有API请求必须使用HTTPS</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">IP白名单</h4>
                            <p class="text-xs text-gray-500">启用IP白名单访问控制</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">API密钥过期</h4>
                            <p class="text-xs text-gray-500">自动过期未使用的API密钥</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 flex justify-end">
            <button class="gradient-bg text-white px-6 py-2 rounded-lg font-medium hover:opacity-90">
                保存配置
            </button>
        </div>
    </div>
</div>

<!-- 用户密钥管理模态框 -->
<div id="keyManagementModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">密钥管理</h3>
                    <button onclick="closeKeyManagementModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="mb-4">
                    <h4 class="text-md font-medium text-gray-800">用户: <span id="keyUser">张开发者</span></h4>
                </div>

                <!-- 密钥列表 -->
                <div class="bg-white rounded-lg shadow-sm mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h5 class="font-medium text-gray-900">密钥列表</h5>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                            <tr>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    密钥名称
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Access Key
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    状态
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    权限类型
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    创建时间
                                </th>
                                <th scope="col"
                                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    操作
                                </th>
                            </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                            <!-- 示例密钥数据 -->
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">默认密钥</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ak-1234567890abcdef</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">启用</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">部分权限</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button onclick="openEditKeyPermission('ak-1234567890abcdef')"
                                            class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-edit mr-1"></i>编辑权限
                                    </button>
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </div>

                    <!-- 统计信息 -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="text-sm text-gray-700">
                            共 <span class="font-medium">1</span> 条密钥记录
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑密钥权限模态框 -->
<div id="editKeyPermissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">编辑密钥权限</h3>
                    <button onclick="closeEditKeyPermissionModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form class="space-y-6">
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">密钥名称</label>
                            <input type="text" id="keyName" value="默认密钥"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Access Key</label>
                            <input type="text" id="accessKey" value="ak-1234567890abcdef" readonly
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-gray-100">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">访问权限</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input id="permission-all" name="permission-type" type="radio"
                                           class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                                    <label for="permission-all" class="ml-3 block text-sm text-gray-700">
                                        全权限（可访问所有API）
                                    </label>
                                </div>
                                <div class="flex items-center">
                                    <input id="permission-partial" name="permission-type" type="radio" checked
                                           class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300">
                                    <label for="permission-partial" class="ml-3 block text-sm text-gray-700">
                                        部分权限（仅可访问指定API）
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div id="apiPermissionSection" class="border border-gray-200 rounded-lg p-4">
                            <label class="block text-sm font-medium text-gray-700 mb-3">选择可访问的API</label>
                            <div class="max-h-80 overflow-y-auto">
                                <div class="space-y-4">
                                    <!-- 用户管理系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-gray-800 flex items-center">
                                                <i class="fas fa-users text-blue-500 mr-2"></i>用户管理系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemPermissions('user')"
                                                        class="text-xs text-blue-600 hover:text-blue-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemPermissions('user')"
                                                        class="text-xs text-blue-600 hover:text-blue-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 pl-6">
                                            <div class="flex items-center">
                                                <input id="perm-user-info" name="apiPermissions" value="user-info" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-user-info" class="ml-2 block text-sm text-gray-700">
                                                    用户信息查询 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-user-create" name="apiPermissions" value="user-create" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-user-create" class="ml-2 block text-sm text-gray-700">
                                                    创建用户 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-user-update" name="apiPermissions" value="user-update" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-user-update" class="ml-2 block text-sm text-gray-700">
                                                    更新用户信息 <span class="text-xs text-gray-500">(PUT)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-user-delete" name="apiPermissions" value="user-delete" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-user-delete" class="ml-2 block text-sm text-gray-700">
                                                    删除用户 <span class="text-xs text-gray-500">(DELETE)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-user-list" name="apiPermissions" value="user-list" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-user-list" class="ml-2 block text-sm text-gray-700">
                                                    用户列表查询 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 订单管理系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-gray-800 flex items-center">
                                                <i class="fas fa-shopping-cart text-green-500 mr-2"></i>订单管理系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemPermissions('order')"
                                                        class="text-xs text-green-600 hover:text-green-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemPermissions('order')"
                                                        class="text-xs text-green-600 hover:text-green-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 pl-6">
                                            <div class="flex items-center">
                                                <input id="perm-order-query" name="apiPermissions" value="order-query" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-order-query" class="ml-2 block text-sm text-gray-700">
                                                    订单查询 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-order-create" name="apiPermissions" value="order-create" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-order-create" class="ml-2 block text-sm text-gray-700">
                                                    创建订单 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-order-update" name="apiPermissions" value="order-update" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-order-update" class="ml-2 block text-sm text-gray-700">
                                                    更新订单状态 <span class="text-xs text-gray-500">(PUT)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-order-cancel" name="apiPermissions" value="order-cancel" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-order-cancel" class="ml-2 block text-sm text-gray-700">
                                                    取消订单 <span class="text-xs text-gray-500">(DELETE)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-order-list" name="apiPermissions" value="order-list" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-order-list" class="ml-2 block text-sm text-gray-700">
                                                    订单列表查询 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 库存管理系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-gray-800 flex items-center">
                                                <i class="fas fa-boxes text-orange-500 mr-2"></i>库存管理系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemPermissions('inventory')"
                                                        class="text-xs text-orange-600 hover:text-orange-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemPermissions('inventory')"
                                                        class="text-xs text-orange-600 hover:text-orange-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 pl-6">
                                            <div class="flex items-center">
                                                <input id="perm-inventory-query" name="apiPermissions" value="inventory-query" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-inventory-query" class="ml-2 block text-sm text-gray-700">
                                                    库存查询 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-inventory-update" name="apiPermissions" value="inventory-update" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-inventory-update" class="ml-2 block text-sm text-gray-700">
                                                    更新库存 <span class="text-xs text-gray-500">(PUT)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-inventory-reserve" name="apiPermissions" value="inventory-reserve" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-inventory-reserve" class="ml-2 block text-sm text-gray-700">
                                                    库存预留 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-inventory-release" name="apiPermissions" value="inventory-release" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-inventory-release" class="ml-2 block text-sm text-gray-700">
                                                    释放库存 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 支付系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-gray-800 flex items-center">
                                                <i class="fas fa-credit-card text-purple-500 mr-2"></i>支付系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemPermissions('payment')"
                                                        class="text-xs text-purple-600 hover:text-purple-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemPermissions('payment')"
                                                        class="text-xs text-purple-600 hover:text-purple-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 pl-6">
                                            <div class="flex items-center">
                                                <input id="perm-payment-create" name="apiPermissions" value="payment-create" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-payment-create" class="ml-2 block text-sm text-gray-700">
                                                    创建支付 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-payment-query" name="apiPermissions" value="payment-query" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-payment-query" class="ml-2 block text-sm text-gray-700">
                                                    支付查询 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-payment-refund" name="apiPermissions" value="payment-refund" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-payment-refund" class="ml-2 block text-sm text-gray-700">
                                                    申请退款 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-payment-callback" name="apiPermissions" value="payment-callback" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-payment-callback" class="ml-2 block text-sm text-gray-700">
                                                    支付回调 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 数据分析系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="text-sm font-medium text-gray-800 flex items-center">
                                                <i class="fas fa-chart-bar text-red-500 mr-2"></i>数据分析系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemPermissions('analytics')"
                                                        class="text-xs text-red-600 hover:text-red-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemPermissions('analytics')"
                                                        class="text-xs text-red-600 hover:text-red-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 pl-6">
                                            <div class="flex items-center">
                                                <input id="perm-analytics-report" name="apiPermissions" value="analytics-report" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-analytics-report" class="ml-2 block text-sm text-gray-700">
                                                    生成报表 <span class="text-xs text-gray-500">(POST)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-analytics-stats" name="apiPermissions" value="analytics-stats" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-analytics-stats" class="ml-2 block text-sm text-gray-700">
                                                    统计数据 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                            <div class="flex items-center">
                                                <input id="perm-analytics-export" name="apiPermissions" value="analytics-export" type="checkbox"
                                                       class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
                                                <label for="perm-analytics-export" class="ml-2 block text-sm text-gray-700">
                                                    导出数据 <span class="text-xs text-gray-500">(GET)</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 全局操作按钮 -->
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="flex justify-between">
                                        <div class="flex space-x-2">
                                            <button type="button" onclick="selectAllPermissions()"
                                                    class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">全选所有</button>
                                            <span class="text-sm text-gray-300">|</span>
                                            <button type="button" onclick="clearAllPermissions()"
                                                    class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">清空所有</button>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            已选择: <span id="selectedPermissionCount">0</span> 个接口
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeEditKeyPermissionModal()"
                                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="button" onclick="saveKeyPermission()"
                                class="gradient-bg text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium hover:opacity-90">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- API注册/编辑模态框 -->
<div id="apiModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="apiModalTitle" class="text-lg font-semibold text-gray-900">注册新API</h3>
                    <button onclick="closeApiModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form id="apiForm" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="form-section active">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="lg:col-span-2">
                                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>基本信息
                                </h4>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">接口名称 <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="interfaceName" name="interfaceName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入接口名称，如：用户信息查询">
                                <p class="text-xs text-gray-500 mt-1">建议使用简洁明了的名称，便于开发者理解</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">请求方法 <span
                                        class="text-red-500">*</span></label>
                                <select id="method" name="method" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="">请选择请求方法</option>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">接口host <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="interfacePath" name="interfacePath" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="如：/api/v1/user/info/{userId}">
                                <p class="text-xs text-gray-500 mt-1">如：http://example.com</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">接口路径 <span
                                        class="text-red-500">*</span></label>
                                <input type="text" id="interfacePath" name="interfacePath" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="如：/api/v1/user/info/{userId}">
                                <p class="text-xs text-gray-500 mt-1">路径参数使用 {参数名} 格式，如：{userId}</p>
                            </div>


                            <!--<div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">接口版本</label>
                                <input type="text" id="version" name="version" value="v1"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="如：v1, v2">
                            </div>-->

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属业务系统 <span
                                        class="text-red-500">*</span></label>
                                <select id="businessSystem" name="businessSystem" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="">请选择业务系统</option>
                                    <option value="用户管理系统">用户管理系统</option>
                                    <option value="订单管理系统">订单管理系统</option>
                                    <option value="库存管理系统">库存管理系统</option>
                                    <option value="财务管理系统">财务管理系统</option>
                                    <option value="AI服务类">AI服务类</option>
                                    <option value="消息通知系统">消息通知系统</option>
                                    <option value="文件管理系统">文件管理系统</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API分类 <span
                                        class="text-red-500">*</span></label>
                                <select id="categoryId" name="categoryId" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="">请选择API分类</option>
                                    <optgroup label="用户管理系统">
                                        <option value="7">用户信息</option>
                                        <option value="8">权限管理</option>
                                        <option value="9">组织架构</option>
                                    </optgroup>
                                    <optgroup label="订单管理系统">
                                        <option value="10">订单处理</option>
                                        <option value="11">支付管理</option>
                                        <option value="12">物流跟踪</option>
                                    </optgroup>
                                    <optgroup label="库存管理系统">
                                        <option value="13">库存查询</option>
                                        <option value="14">出入库管理</option>
                                        <option value="15">盘点管理</option>
                                    </optgroup>
                                    <optgroup label="财务管理系统">
                                        <option value="16">财务报表</option>
                                        <option value="17">成本分析</option>
                                        <option value="18">预算管理</option>
                                    </optgroup>
                                    <optgroup label="AI服务类">
                                        <option value="19">自然语言处理</option>
                                        <option value="20">计算机视觉</option>
                                        <option value="21">机器学习</option>
                                    </optgroup>
                                    <optgroup label="其他系统">
                                        <option value="4">消息通知</option>
                                        <option value="5">文件管理</option>
                                        <option value="6">系统配置</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 接口描述 -->
                    <div class="form-section">
                        <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-file-alt text-green-500 mr-2"></i>接口描述
                        </h4>
                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">功能描述</label>
                                <textarea id="description" name="description" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                          placeholder="请详细描述接口的功能和用途"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- API参数定义 -->
                    <div class="form-section">
                        <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-list-ul text-blue-500 mr-2"></i>API参数定义
                        </h4>

                        <!-- 请求参数 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <label class="block text-sm font-medium text-gray-700">请求参数</label>
                                <button type="button" onclick="addRequestParameter()"
                                        class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-plus mr-1"></i>添加参数
                                </button>
                            </div>
                            <div id="requestParameters" class="space-y-2">
                                <!-- 请求参数表格头 -->
                                <div class="grid grid-cols-12 gap-2 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 px-3 py-2 rounded">
                                    <div class="col-span-3">参数名</div>
                                    <div class="col-span-2">类型</div>
                                    <div class="col-span-1">必填</div>
                                    <div class="col-span-4">描述</div>
                                    <div class="col-span-2">操作</div>
                                </div>
                                <!-- 默认参数行 -->
                                <div class="request-param-row" data-level="0">
                                    <div class="grid grid-cols-12 gap-2 items-center">
                                        <div class="col-span-3">
                                            <input type="text" name="reqParamName[]"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                                   placeholder="参数名">
                                        </div>
                                        <div class="col-span-2">
                                            <select name="reqParamType[]" onchange="handleTypeChange(this)"
                                                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                                <option value="string">String</option>
                                                <option value="integer">Integer</option>
                                                <option value="boolean">Boolean</option>
                                                <option value="array">Array</option>
                                                <option value="object">Object</option>
                                            </select>
                                        </div>
                                        <div class="col-span-1">
                                            <input type="checkbox" name="reqParamRequired[]"
                                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </div>
                                        <div class="col-span-4">
                                            <input type="text" name="reqParamDesc[]"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                                   placeholder="参数描述">
                                        </div>
                                        <div class="col-span-2 flex space-x-1">
                                            <button type="button" onclick="addChildParameter(this, 'request')"
                                                    class="add-child-btn text-green-600 hover:text-green-500 text-sm"
                                                    title="添加子参数" style="display: none;">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                            <button type="button" onclick="removeParameter(this)"
                                                    class="text-red-600 hover:text-red-500 text-sm" title="删除参数">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- 子参数容器 -->
                                    <div class="children-container ml-6"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 响应参数 -->
                        <div>
                            <div class="flex items-center justify-between mb-3">
                                <label class="block text-sm font-medium text-gray-700">响应参数</label>
                                <button type="button" onclick="addResponseParameter()"
                                        class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-plus mr-1"></i>添加参数
                                </button>
                            </div>
                            <div id="responseParameters" class="space-y-2">
                                <!-- 响应参数表格头 -->
                                <div class="grid grid-cols-12 gap-2 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 px-3 py-2 rounded">
                                    <div class="col-span-3">参数名</div>
                                    <div class="col-span-2">类型</div>
                                    <div class="col-span-1">必返回</div>
                                    <div class="col-span-4">描述</div>
                                    <div class="col-span-2">操作</div>
                                </div>
                                <!-- 默认参数行 -->
                                <div class="response-param-row" data-level="0">
                                    <div class="grid grid-cols-12 gap-2 items-center">
                                        <div class="col-span-3">
                                            <input type="text" name="resParamName[]"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                                   placeholder="参数名">
                                        </div>
                                        <div class="col-span-2">
                                            <select name="resParamType[]" onchange="handleTypeChange(this)"
                                                    class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                                <option value="string">String</option>
                                                <option value="integer">Integer</option>
                                                <option value="boolean">Boolean</option>
                                                <option value="array">Array</option>
                                                <option value="object">Object</option>
                                            </select>
                                        </div>
                                        <div class="col-span-1">
                                            <input type="checkbox" name="resParamRequired[]"
                                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        </div>
                                        <div class="col-span-4">
                                            <input type="text" name="resParamDesc[]"
                                                   class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                                                   placeholder="参数描述">
                                        </div>
                                        <div class="col-span-2 flex space-x-1">
                                            <button type="button" onclick="addChildParameter(this, 'response')"
                                                    class="add-child-btn text-green-600 hover:text-green-500 text-sm"
                                                    title="添加子参数" style="display: none;">
                                                <i class="fas fa-plus-circle"></i>
                                            </button>
                                            <button type="button" onclick="removeParameter(this)"
                                                    class="text-red-600 hover:text-red-500 text-sm" title="删除参数">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <!-- 子参数容器 -->
                                    <div class="children-container ml-6"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 请求示例 -->
                    <div class="form-section">
                        <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-code text-purple-500 mr-2"></i>请求示例
                        </h4>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">请求示例</label>
                                <textarea id="requestExample" name="requestExample" rows="6"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 font-mono text-sm"
                                          placeholder='请输入请求示例，如：
{
  "userId": "12345",
  "includePermissions": true
}'></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">响应示例</label>
                                <textarea id="responseExample" name="responseExample" rows="6"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 font-mono text-sm"
                                          placeholder='请输入响应示例，如：
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "12345",
    "username": "zhangsan",
    "email": "<EMAIL>"
  }
}'></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 状态设置 -->
                    <div class="form-section">
                        <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-cog text-orange-500 mr-2"></i>状态设置
                        </h4>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">接口状态</label>
                                <select id="status" name="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="1">启用</option>
                                    <option value="2">禁用</option>
                                </select>
                            </div>

                            <!--<div class="flex items-center">
                                <input type="checkbox" id="isDeprecated" name="isDeprecated"
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="isDeprecated" class="ml-2 text-sm text-gray-700">标记为已废弃</label>
                            </div>-->
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <button type="button" onclick="closeApiModal()"
                                class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                class="gradient-bg text-white px-6 py-2 rounded-lg font-medium hover:opacity-90">
                            <span id="submitButtonText">注册API</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 用户管理模态框 -->
<div id="userModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="userModalTitle" class="text-lg font-semibold text-gray-900">新增用户</h3>
                    <button onclick="closeUserModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form id="userForm" class="space-y-6">
                    <!-- 用户信息 -->
                    <div class="form-section active">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="lg:col-span-2">
                                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                                    <i class="fas fa-user text-blue-500 mr-2"></i>用户信息
                                </h4>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户名 <span class="text-red-500">*</span></label>
                                <input type="text" id="username" name="username" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入用户名">
                                <p class="text-xs text-gray-500 mt-1">用户名将用于登录系统</p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">用户状态</label>
                                <select id="userStatus" name="userStatus"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="1">启用</option>
                                    <option value="2">禁用</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">手机号码 <span class="text-red-500">*</span></label>
                                <input type="tel" id="phone" name="phone"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入手机号码">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">邮箱地址 <span class="text-red-500">*</span></label>
                                <input type="email" id="email" name="email" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入邮箱地址">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">企业名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="companyName" name="companyName" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入企业名称">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码</label>
                                <input type="text" id="creditCode" name="creditCode"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入统一社会信用代码">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">企业联系人</label>
                                <input type="text" id="legalPerson" name="legalPerson"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                       placeholder="请输入联系人">
                            </div>


                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属行业</label>
                                <select id="industry" name="industry"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option value="">请选择所属行业</option>
                                    <option value="technology">科技/互联网</option>
                                    <option value="finance">金融/保险</option>
                                    <option value="manufacturing">制造业</option>
                                    <option value="retail">零售/电商</option>
                                    <option value="education">教育/培训</option>
                                    <option value="healthcare">医疗/健康</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>

                            <div class="lg:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">备注信息</label>
                                <textarea id="userRemark" name="userRemark" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                          placeholder="请输入备注信息"></textarea>
                            </div>

                        </div>
                    </div>



                    <!-- 操作按钮 -->
                    <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                        <button type="button" onclick="closeUserModal()"
                                class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit"
                                class="gradient-bg text-white px-6 py-2 rounded-lg font-medium hover:opacity-90">
                            <span id="userSubmitButtonText">新增用户</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 新建限流规则模态框 (已废弃，使用新的API配置方式) -->
<!--<div id="rateLimitModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 id="rateLimitModalTitle" class="text-lg font-semibold text-gray-900">新建限流规则</h3>
                    <button onclick="closeRateLimitModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form id="rateLimitForm" class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规则名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="ruleName" name="ruleName" required placeholder="请输入规则名称"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                            <select id="ruleStatus" name="ruleStatus"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="enabled">启用</option>
                                <option value="disabled">禁用</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规则类型 <span class="text-red-500">*</span></label>
                            <select id="ruleType" name="ruleType" required onchange="handleRuleTypeChange()"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                <option value="">请选择规则类型</option>
                                <option value="global">全局规则</option>
                                <option value="user">用户规则</option>
                                <option value="api">接口规则</option>
                            </select>
                        </div>

                        <!-- 用户选择（仅用户规则时显示） -->
                        <div id="userSelection" class="lg:col-span-2" style="display: none;">
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择用户 <span class="text-red-500">*</span></label>
                            <div class="border border-gray-300 rounded-lg p-4">
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="targetUsers" value="zhang"
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-700">
                                            <span class="font-medium">张开发者</span>
                                            <span class="text-gray-500 block">科技有限公司 | <EMAIL></span>
                                        </span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="targetUsers" value="li"
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-700">
                                            <span class="font-medium">李工程师</span>
                                            <span class="text-gray-500 block">创新科技公司 | <EMAIL></span>
                                        </span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="targetUsers" value="wang"
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                        <span class="ml-3 text-sm text-gray-700">
                                            <span class="font-medium">王企业</span>
                                            <span class="text-gray-500 block">智能制造有限公司 | <EMAIL></span>
                                        </span>
                                    </label>
                                </div>
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <div class="flex space-x-2">
                                        <button type="button" onclick="selectAllUsers()"
                                                class="text-xs text-indigo-600 hover:text-indigo-500">全选</button>
                                        <span class="text-xs text-gray-300">|</span>
                                        <button type="button" onclick="clearAllUsers()"
                                                class="text-xs text-indigo-600 hover:text-indigo-500">清空</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- API接口选择（仅接口规则时显示） -->
                        <div id="apiSelection" class="lg:col-span-2" style="display: none;">
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择API接口 <span class="text-red-500">*</span></label>
                            <div class="border border-gray-300 rounded-lg p-4 max-h-80 overflow-y-auto">
                                <div class="space-y-4">
                                    <!-- 用户管理系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-gray-900 flex items-center">
                                                <i class="fas fa-users text-blue-500 mr-2"></i>用户管理系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemApis('user')"
                                                        class="text-xs text-blue-600 hover:text-blue-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemApis('user')"
                                                        class="text-xs text-blue-600 hover:text-blue-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="ml-6 space-y-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="user-info"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">用户信息查询 (GET /api/v1/user/info)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="user-create"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">创建用户 (POST /api/v1/user)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="user-update"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">更新用户信息 (PUT /api/v1/user)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="user-delete"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">删除用户 (DELETE /api/v1/user)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="user-list"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">用户列表查询 (GET /api/v1/users)</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- 订单管理系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-gray-900 flex items-center">
                                                <i class="fas fa-shopping-cart text-green-500 mr-2"></i>订单管理系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemApis('order')"
                                                        class="text-xs text-green-600 hover:text-green-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemApis('order')"
                                                        class="text-xs text-green-600 hover:text-green-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="ml-6 space-y-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="order-query"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">订单查询 (GET /api/v1/order/info)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="order-create"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">创建订单 (POST /api/v1/order)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="order-update"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">更新订单状态 (PUT /api/v1/order/status)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="order-cancel"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">取消订单 (DELETE /api/v1/order)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="order-list"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">订单列表查询 (GET /api/v1/orders)</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- 库存管理系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-gray-900 flex items-center">
                                                <i class="fas fa-boxes text-orange-500 mr-2"></i>库存管理系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemApis('inventory')"
                                                        class="text-xs text-orange-600 hover:text-orange-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemApis('inventory')"
                                                        class="text-xs text-orange-600 hover:text-orange-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="ml-6 space-y-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="inventory-query"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">库存查询 (GET /api/v1/inventory)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="inventory-update"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">更新库存 (PUT /api/v1/inventory)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="inventory-reserve"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">库存预留 (POST /api/v1/inventory/reserve)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="inventory-release"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">释放库存 (POST /api/v1/inventory/release)</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- 支付系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-gray-900 flex items-center">
                                                <i class="fas fa-credit-card text-purple-500 mr-2"></i>支付系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemApis('payment')"
                                                        class="text-xs text-purple-600 hover:text-purple-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemApis('payment')"
                                                        class="text-xs text-purple-600 hover:text-purple-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="ml-6 space-y-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="payment-create"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">创建支付 (POST /api/v1/payment)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="payment-query"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">支付查询 (GET /api/v1/payment)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="payment-refund"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">申请退款 (POST /api/v1/payment/refund)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="payment-callback"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">支付回调 (POST /api/v1/payment/callback)</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- 数据分析系统 -->
                                    <div>
                                        <div class="flex items-center justify-between mb-2">
                                            <h4 class="font-medium text-gray-900 flex items-center">
                                                <i class="fas fa-chart-bar text-red-500 mr-2"></i>数据分析系统
                                            </h4>
                                            <div class="flex space-x-2">
                                                <button type="button" onclick="selectSystemApis('analytics')"
                                                        class="text-xs text-red-600 hover:text-red-500">全选</button>
                                                <span class="text-xs text-gray-300">|</span>
                                                <button type="button" onclick="clearSystemApis('analytics')"
                                                        class="text-xs text-red-600 hover:text-red-500">清空</button>
                                            </div>
                                        </div>
                                        <div class="ml-6 space-y-1">
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="analytics-report"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">生成报表 (POST /api/v1/analytics/report)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="analytics-stats"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">统计数据 (GET /api/v1/analytics/stats)</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="targetApis" value="analytics-export"
                                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                                <span class="ml-2 text-sm text-gray-700">导出数据 (GET /api/v1/analytics/export)</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- 全局操作按钮 -->
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <div class="flex justify-between">
                                        <div class="flex space-x-2">
                                            <button type="button" onclick="selectAllApis()"
                                                    class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">全选所有</button>
                                            <span class="text-sm text-gray-300">|</span>
                                            <button type="button" onclick="clearAllApis()"
                                                    class="text-sm text-indigo-600 hover:text-indigo-500 font-medium">清空所有</button>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            已选择: <span id="selectedApiCount">0</span> 个接口
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        </div>

                </form>
            </div>
        </div>
    </div>
</div>-->

<script>
    // 切换标签页
    function switchTab(tabName) {
        // 隐藏所有内容
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });

        // 重置所有标签样式
        document.querySelectorAll('[id$="-tab"]').forEach(tab => {
            tab.classList.remove('tab-active');
            tab.classList.add('text-gray-500', 'hover:text-gray-700');
        });

        // 显示选中的内容
        document.getElementById(tabName + '-content').classList.remove('hidden');

        // 激活选中的标签
        const activeTab = document.getElementById(tabName + '-tab');
        activeTab.classList.add('tab-active');
        activeTab.classList.remove('text-gray-500', 'hover:text-gray-700');
    }

    // ========================================
    // API限流配置管理相关函数
    // ========================================

    // 切换业务系统分组显示/隐藏
    function toggleSystemGroup(systemId) {
        const apiGroup = document.getElementById(systemId + '_apis');
        const icon = document.getElementById(systemId + '_icon');

        if (apiGroup.style.display === 'none') {
            apiGroup.style.display = 'block';
            icon.style.transform = 'rotate(180deg)';
        } else {
            apiGroup.style.display = 'none';
            icon.style.transform = 'rotate(0deg)';
        }
    }

    // 自动保存API限流配置
    function autoSaveApiRateLimit(apiId, element) {
        const row = element.closest('.api-item');
        const limitTypeSelect = row.querySelector('select');
        const limitCountInput = row.querySelector('input[type="number"]');

        const limitType = limitTypeSelect.value;
        const limitCount = limitCountInput.value;

        // 验证输入
        if (!limitCount || limitCount <= 0) {
            alert('请输入有效的限流次数');
            return;
        }

        // 构建配置数据
        const config = {
            apiId: apiId,
            limitType: limitType,
            limitCount: parseInt(limitCount)
        };

        // 模拟自动保存到后端
        console.log(`自动保存API ${apiId} 限流配置:`, config);

        // 显示保存成功提示
        showAutoSaveSuccess(element);

        // 这里可以添加实际的API调用
        // fetch('/api/rate-limit/save', {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify(config)
        // });
    }

    // 显示自动保存成功提示
    function showAutoSaveSuccess(element) {
        // 创建临时的保存成功提示
        const saveIndicator = document.createElement('div');
        saveIndicator.className = 'absolute top-0 right-0 bg-green-500 text-white text-xs px-2 py-1 rounded-bl transform transition-all duration-300';
        saveIndicator.innerHTML = '<i class="fas fa-check mr-1"></i>已保存';

        // 添加到相对定位的容器
        const container = element.closest('.api-item');
        container.style.position = 'relative';
        container.appendChild(saveIndicator);

        // 2秒后移除提示
        setTimeout(() => {
            if (saveIndicator.parentNode) {
                saveIndicator.parentNode.removeChild(saveIndicator);
            }
        }, 2000);
    }

    // 筛选API
    function filterApis() {
        const systemFilter = document.getElementById('businessSystemFilter').value;
        const searchInput = document.getElementById('apiSearchInput').value.toLowerCase();

        const systemGroups = document.querySelectorAll('.business-system-group');

        systemGroups.forEach(group => {
            const systemId = group.dataset.system;
            let shouldShowSystem = false;

            // 系统筛选
            if (!systemFilter || systemFilter === systemId) {
                const apiItems = group.querySelectorAll('.api-item');

                apiItems.forEach(item => {
                    const apiName = item.dataset.apiName.toLowerCase();
                    const shouldShowApi = !searchInput || apiName.includes(searchInput);

                    item.style.display = shouldShowApi ? 'block' : 'none';
                    if (shouldShowApi) shouldShowSystem = true;
                });
            }

            group.style.display = shouldShowSystem ? 'block' : 'none';
        });
    }

    // 重置筛选
    function resetFilters() {
        document.getElementById('businessSystemFilter').value = '';
        document.getElementById('apiSearchInput').value = '';

        // 显示所有系统和API
        const systemGroups = document.querySelectorAll('.business-system-group');
        const apiItems = document.querySelectorAll('.api-item');

        systemGroups.forEach(group => group.style.display = 'block');
        apiItems.forEach(item => item.style.display = 'block');
    }

    // ========================================
    // 原有限流规则管理相关函数（保留兼容性）
    // ========================================

    // 打开限流规则弹窗
    function openRateLimitModal(mode, ruleId = null) {
        console.log('openRateLimitModal called with mode:', mode, 'ruleId:', ruleId);

        const modal = document.getElementById('rateLimitModal');
        const title = document.getElementById('rateLimitModalTitle');
        const submitButton = document.getElementById('rateLimitSubmitButtonText');
        const form = document.getElementById('rateLimitForm');

        if (!modal || !title || !submitButton || !form) {
            console.error('限流规则弹框元素未找到');
            return;
        }

        // 重置表单
        form.reset();

        // 隐藏条件选择区域
        document.getElementById('userSelection').style.display = 'none';
        document.getElementById('apiSelection').style.display = 'none';

        if (mode === 'create') {
            title.textContent = '新建限流规则';
            submitButton.textContent = '创建规则';
            // 设置默认值
            document.getElementById('ruleStatus').value = 'enabled';
        } else if (mode === 'edit') {
            title.textContent = '编辑限流规则';
            submitButton.textContent = '更新规则';
            // 加载规则数据
            loadRateLimitRuleData(ruleId);
        }

        modal.classList.remove('hidden');

        // 设置焦点
        setTimeout(() => {
            const firstInput = modal.querySelector('input');
            if (firstInput) {
                firstInput.focus();
            }
        }, 200);
    }

    // 关闭限流规则弹窗
    function closeRateLimitModal() {
        const modal = document.getElementById('rateLimitModal');
        const form = document.getElementById('rateLimitForm');

        if (modal) {
            modal.classList.add('hidden');
        }

        if (form) {
            form.reset();
            // 隐藏条件选择区域
            document.getElementById('userSelection').style.display = 'none';
            document.getElementById('apiSelection').style.display = 'none';
        }
    }

    // 处理规则类型变化
    function handleRuleTypeChange() {
        const ruleType = document.getElementById('ruleType').value;
        const userSelection = document.getElementById('userSelection');
        const apiSelection = document.getElementById('apiSelection');

        // 隐藏所有条件选择
        userSelection.style.display = 'none';
        apiSelection.style.display = 'none';

        // 清除之前的选择
        const userCheckboxes = document.querySelectorAll('input[name="targetUsers"]');
        userCheckboxes.forEach(checkbox => checkbox.checked = false);
        const apiCheckboxes = document.querySelectorAll('input[name="targetApis"]');
        apiCheckboxes.forEach(checkbox => checkbox.checked = false);

        // 更新API选择计数
        updateSelectedApiCount();

        // 根据规则类型显示对应的选择区域
        if (ruleType === 'user') {
            userSelection.style.display = 'block';
        } else if (ruleType === 'api') {
            apiSelection.style.display = 'block';
        }
    }



    // ========================================
    // 多选功能相关函数
    // ========================================

    // 用户选择相关函数
    function selectAllUsers() {
        const userCheckboxes = document.querySelectorAll('input[name="targetUsers"]');
        userCheckboxes.forEach(checkbox => checkbox.checked = true);
    }

    function clearAllUsers() {
        const userCheckboxes = document.querySelectorAll('input[name="targetUsers"]');
        userCheckboxes.forEach(checkbox => checkbox.checked = false);
    }

    // API接口选择相关函数
    function selectSystemApis(system) {
        const systemApis = {
            'user': ['user-info', 'user-create', 'user-update', 'user-delete', 'user-list'],
            'order': ['order-query', 'order-create', 'order-update', 'order-cancel', 'order-list'],
            'inventory': ['inventory-query', 'inventory-update', 'inventory-reserve', 'inventory-release'],
            'payment': ['payment-create', 'payment-query', 'payment-refund', 'payment-callback'],
            'analytics': ['analytics-report', 'analytics-stats', 'analytics-export']
        };

        const apis = systemApis[system] || [];
        apis.forEach(apiValue => {
            const checkbox = document.querySelector(`input[name="targetApis"][value="${apiValue}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        updateSelectedApiCount();
    }

    function clearSystemApis(system) {
        const systemApis = {
            'user': ['user-info', 'user-create', 'user-update', 'user-delete', 'user-list'],
            'order': ['order-query', 'order-create', 'order-update', 'order-cancel', 'order-list'],
            'inventory': ['inventory-query', 'inventory-update', 'inventory-reserve', 'inventory-release'],
            'payment': ['payment-create', 'payment-query', 'payment-refund', 'payment-callback'],
            'analytics': ['analytics-report', 'analytics-stats', 'analytics-export']
        };

        const apis = systemApis[system] || [];
        apis.forEach(apiValue => {
            const checkbox = document.querySelector(`input[name="targetApis"][value="${apiValue}"]`);
            if (checkbox) {
                checkbox.checked = false;
            }
        });
        updateSelectedApiCount();
    }

    function selectAllApis() {
        const apiCheckboxes = document.querySelectorAll('input[name="targetApis"]');
        apiCheckboxes.forEach(checkbox => checkbox.checked = true);
        updateSelectedApiCount();
    }

    function clearAllApis() {
        const apiCheckboxes = document.querySelectorAll('input[name="targetApis"]');
        apiCheckboxes.forEach(checkbox => checkbox.checked = false);
        updateSelectedApiCount();
    }

    function updateSelectedApiCount() {
        const selectedApis = document.querySelectorAll('input[name="targetApis"]:checked');
        const countElement = document.getElementById('selectedApiCount');
        if (countElement) {
            countElement.textContent = selectedApis.length;
        }
    }

    // 加载限流规则数据（编辑模式）
    function loadRateLimitRuleData(ruleId) {
        // 模拟限流规则数据
        const mockRuleData = {
            'global-rule-1': {
                ruleName: '全局QPS限制',
                ruleType: 'global',
                limitType: 'second',
                limitCount: 1000,
                ruleStatus: 'enabled',
                ruleDescription: '全局每秒最多1000次请求'
            },
            'user-rule-1': {
                ruleName: '用户请求限制',
                ruleType: 'user',
                targetUsers: ['zhang', 'li'],
                limitType: 'minute',
                limitCount: 100,
                ruleStatus: 'enabled',
                ruleDescription: '指定用户每分钟最多100次请求'
            },
            'api-rule-1': {
                ruleName: '用户管理接口限制',
                ruleType: 'api',
                targetApis: ['user-info', 'user-create', 'user-update'],
                limitType: 'second',
                limitCount: 50,
                ruleStatus: 'disabled',
                ruleDescription: '用户管理相关接口每秒最多50次请求'
            }
        };

        const ruleData = mockRuleData[ruleId];
        if (ruleData) {
            // 填充基本信息
            document.getElementById('ruleName').value = ruleData.ruleName;
            document.getElementById('ruleType').value = ruleData.ruleType;
            document.getElementById('limitType').value = ruleData.limitType;
            document.getElementById('limitCount').value = ruleData.limitCount;
            document.getElementById('ruleStatus').value = ruleData.ruleStatus;
            document.getElementById('ruleDescription').value = ruleData.ruleDescription || '';

            // 触发规则类型变化
            handleRuleTypeChange();

            // 根据规则类型填充对应的选择项
            if (ruleData.ruleType === 'user' && ruleData.targetUsers) {
                setTimeout(() => {
                    // 选中对应的用户复选框
                    ruleData.targetUsers.forEach(userId => {
                        const userCheckbox = document.querySelector(`input[name="targetUsers"][value="${userId}"]`);
                        if (userCheckbox) {
                            userCheckbox.checked = true;
                        }
                    });
                }, 100);
            } else if (ruleData.ruleType === 'api' && ruleData.targetApis) {
                setTimeout(() => {
                    // 选中对应的API接口复选框
                    ruleData.targetApis.forEach(apiId => {
                        const apiCheckbox = document.querySelector(`input[name="targetApis"][value="${apiId}"]`);
                        if (apiCheckbox) {
                            apiCheckbox.checked = true;
                        }
                    });
                    updateSelectedApiCount();
                }, 100);
            }
        }
    }

    // 显示限流规则成功消息
    function showRateLimitSuccessMessage(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
        successDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            successDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 300);
        }, 3000);
    }

    // 初始化限流规则管理功能
    const rateLimitModal = document.getElementById('rateLimitModal');
    const rateLimitForm = document.getElementById('rateLimitForm');

    if (rateLimitModal && rateLimitForm) {
        // 处理限流规则表单提交
        rateLimitForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);
            const ruleData = {};
            for (let [key, value] of formData.entries()) {
                ruleData[key] = value;
            }

            // 验证必填字段
            const requiredFields = ['ruleName', 'ruleType', 'limitType', 'limitCount'];

            // 根据规则类型添加额外的验证
            if (ruleData.ruleType === 'user') {
                // 检查是否选择了用户
                const selectedUsers = document.querySelectorAll('input[name="targetUsers"]:checked');
                if (selectedUsers.length === 0) {
                    alert('请至少选择一个目标用户');
                    return;
                }
                ruleData.targetUsers = Array.from(selectedUsers).map(checkbox => checkbox.value);
            } else if (ruleData.ruleType === 'api') {
                // 检查是否选择了API接口
                const selectedApis = document.querySelectorAll('input[name="targetApis"]:checked');
                if (selectedApis.length === 0) {
                    alert('请至少选择一个API接口');
                    return;
                }
                ruleData.targetApis = Array.from(selectedApis).map(checkbox => checkbox.value);
            }

            const missingFields = requiredFields.filter(field => !ruleData[field]);

            if (missingFields.length > 0) {
                alert('请填写所有必填字段');
                return;
            }

            // 验证限制次数
            if (isNaN(ruleData.limitCount) || ruleData.limitCount <= 0) {
                alert('请输入有效的限制次数');
                return;
            }

            // 模拟提交
            const submitButton = document.getElementById('rateLimitSubmitButtonText');
            const submitButtonElement = submitButton.parentElement;
            const originalText = submitButton.textContent;

            // 禁用提交按钮并显示加载状态
            submitButtonElement.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';

            // 模拟API调用
            setTimeout(() => {
                const isCreate = originalText.includes('创建');
                const action = isCreate ? '创建' : '更新';

                // 显示成功消息
                showRateLimitSuccessMessage(`限流规则${action}成功！规则 "${ruleData.ruleName}" 已${action}。`);

                // 重置按钮状态
                submitButtonElement.disabled = false;
                submitButton.textContent = originalText;

                // 关闭弹框
                closeRateLimitModal();

                console.log('限流规则数据：', ruleData);
            }, 2000);
        });

        // 点击模态框外部关闭
        rateLimitModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeRateLimitModal();
            }
        });

        // 为API选择复选框添加变化监听器
        const apiCheckboxes = document.querySelectorAll('input[name="targetApis"]');
        apiCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedApiCount);
        });
    }

    // ========================================
    // API管理相关函数
    // ========================================

    // 按业务系统筛选API
    function filterApisBySystem() {
        const systemFilter = document.getElementById('businessSystemFilter').value;
        const systemGroups = document.querySelectorAll('.api-system-group');

        systemGroups.forEach(group => {
            const systemName = group.dataset.system;
            if (!systemFilter || systemFilter === systemName) {
                group.style.display = 'block';
            } else {
                group.style.display = 'none';
            }
        });
    }

    // 按API名称筛选
    function filterApisByName() {
        const nameFilter = document.getElementById('apiNameFilter').value.toLowerCase();
        const apiItems = document.querySelectorAll('.api-item');
        const systemGroups = document.querySelectorAll('.api-system-group');

        // 先隐藏所有系统组
        systemGroups.forEach(group => {
            let hasVisibleApi = false;
            const groupApiItems = group.querySelectorAll('.api-item');

            groupApiItems.forEach(item => {
                const apiName = item.dataset.apiName.toLowerCase();
                if (!nameFilter || apiName.includes(nameFilter)) {
                    item.style.display = 'block';
                    hasVisibleApi = true;
                } else {
                    item.style.display = 'none';
                }
            });

            // 如果系统组内有可见的API，则显示系统组
            group.style.display = hasVisibleApi ? 'block' : 'none';
        });
    }

    // 重置API筛选
    function resetApiFilters() {
        document.getElementById('businessSystemFilter').value = '';
        document.getElementById('apiNameFilter').value = '';

        // 显示所有系统组和API
        const systemGroups = document.querySelectorAll('.api-system-group');
        const apiItems = document.querySelectorAll('.api-item');

        systemGroups.forEach(group => group.style.display = 'block');
        apiItems.forEach(item => item.style.display = 'block');
    }

    // 保存表单草稿
    function saveFormDraft() {
        const formData = {};
        const form = document.getElementById('apiForm');
        if (!form) return;

        const inputs = form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                formData[input.name] = input.checked;
            } else {
                formData[input.name] = input.value;
            }
        });

        localStorage.setItem('apiFormDraft', JSON.stringify(formData));
    }

    // 加载表单草稿
    function loadFormDraft() {
        const draft = localStorage.getItem('apiFormDraft');
        if (draft) {
            try {
                const formData = JSON.parse(draft);
                const form = document.getElementById('apiForm');
                if (!form) return;

                Object.keys(formData).forEach(key => {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input) {
                        if (input.type === 'checkbox') {
                            input.checked = formData[key];
                        } else {
                            input.value = formData[key];
                        }
                    }
                });

                // 更新预览
                updateApiPreview();
            } catch (e) {
                console.error('加载草稿失败:', e);
            }
        }
    }

    // 清除表单草稿
    function clearFormDraft() {
        localStorage.removeItem('apiFormDraft');
    }

    // 打开API注册/编辑弹框
    function openApiModal(mode, apiId) {
        console.log('openApiModal called with mode:', mode, 'apiId:', apiId);

        try {
            const modal = document.getElementById('apiModal');
            const title = document.getElementById('apiModalTitle');
            const submitButton = document.getElementById('submitButtonText');
            const form = document.getElementById('apiForm');

            if (!modal) {
                console.error('Modal not found');
                return;
            }
            if (!title) {
                console.error('Title not found');
                return;
            }
            if (!submitButton) {
                console.error('Submit button not found');
                return;
            }
            if (!form) {
                console.error('Form not found');
                return;
            }

            console.log('All elements found, proceeding...');

            // 重置表单
            form.reset();

            if (mode === 'create') {
                title.textContent = '注册新API';
                submitButton.textContent = '注册API';

                // 设置默认值
                setTimeout(() => {
                    const versionInput = document.getElementById('version');
                    const statusInput = document.getElementById('status');
                    if (versionInput) versionInput.value = 'v1';
                    if (statusInput) statusInput.value = '1';
                }, 50);

            } else if (mode === 'edit') {
                title.textContent = '编辑API接口';
                submitButton.textContent = '更新API';

                // 加载API数据
                setTimeout(() => {
                    loadApiData(apiId);
                }, 50);
            }

            // 显示弹框
            modal.classList.remove('hidden');
            console.log('Modal should be visible now');

            // 设置焦点
            setTimeout(() => {
                const firstInput = modal.querySelector('input, select, textarea');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 200);

        } catch (error) {
            console.error('Error in openApiModal:', error);
        }
    }


    // 加载API数据（编辑模式）
    function loadApiData(apiId) {
        // 模拟加载API数据
        const mockData = {
            'user-info': {
                interfaceName: '用户信息查询',
                interfacePath: '/api/v1/user/info/{userId}',
                method: 'GET',
                version: 'v1',
                businessSystem: '用户管理系统',
                categoryId: '7',
                description: '根据用户ID查询用户的基本信息，包括姓名、邮箱、部门等',
                requestParameters: [
                    {name: 'userId', type: 'string', required: true, description: '用户ID'},
                    {name: 'includePermissions', type: 'boolean', required: false, description: '是否包含权限信息'}
                ],
                responseParameters: [
                    {name: 'code', type: 'integer', required: true, description: '响应状态码'},
                    {name: 'message', type: 'string', required: true, description: '响应消息'},
                    {
                        name: 'data',
                        type: 'object',
                        required: true,
                        description: '用户数据',
                        children: [
                            {name: 'userId', type: 'string', required: true, description: '用户ID'},
                            {name: 'username', type: 'string', required: true, description: '用户名'},
                            {name: 'email', type: 'string', required: true, description: '邮箱地址'},
                            {
                                name: 'profile',
                                type: 'object',
                                required: false,
                                description: '用户档案',
                                children: [
                                    {name: 'avatar', type: 'string', required: false, description: '头像URL'},
                                    {name: 'phone', type: 'string', required: false, description: '手机号码'}
                                ]
                            }
                        ]
                    }
                ],
                requestExample: '{\n  "userId": "12345",\n  "includePermissions": true\n}',
                responseExample: '{\n  "code": 200,\n  "message": "success",\n  "data": {\n    "userId": "12345",\n    "username": "zhangsan",\n    "email": "<EMAIL>",\n    "department": "技术部"\n  }\n}',
                status: '1',
                isDeprecated: false
            },
            'order-query': {
                interfaceName: '订单查询',
                interfacePath: '/api/v1/order/info/{orderId}',
                method: 'GET',
                version: 'v1',
                businessSystem: '订单管理系统',
                categoryId: '10',
                description: '根据订单ID查询订单详细信息，包括订单状态、商品信息、支付信息等',
                requestParameters: [
                    {name: 'orderId', type: 'string', required: true, description: '订单ID'},
                    {name: 'includeItems', type: 'boolean', required: false, description: '是否包含商品明细'}
                ],
                responseParameters: [
                    {name: 'code', type: 'integer', required: true, description: '响应状态码'},
                    {name: 'message', type: 'string', required: true, description: '响应消息'},
                    {
                        name: 'data',
                        type: 'object',
                        required: true,
                        description: '订单数据',
                        children: [
                            {name: 'orderId', type: 'string', required: true, description: '订单ID'},
                            {name: 'status', type: 'string', required: true, description: '订单状态'},
                            {name: 'totalAmount', type: 'number', required: true, description: '订单总金额'},
                            {
                                name: 'items',
                                type: 'array',
                                required: false,
                                description: '商品列表',
                                children: [
                                    {name: 'productId', type: 'string', required: true, description: '商品ID'},
                                    {name: 'productName', type: 'string', required: true, description: '商品名称'},
                                    {name: 'quantity', type: 'integer', required: true, description: '数量'},
                                    {name: 'price', type: 'number', required: true, description: '单价'}
                                ]
                            }
                        ]
                    }
                ],
                requestExample: '{\n  "orderId": "ORD20240220001",\n  "includeItems": true\n}',
                responseExample: '{\n  "code": 200,\n  "message": "success",\n  "data": {\n    "orderId": "ORD20240220001",\n    "status": "已支付",\n    "totalAmount": 299.00,\n    "items": [...]\n  }\n}',
                status: '1',
                isDeprecated: false
            }
        };

        const data = mockData[apiId];
        if (data) {
            // 加载基本信息
            document.getElementById('interfaceName').value = data.interfaceName;
            document.getElementById('interfacePath').value = data.interfacePath;
            document.getElementById('method').value = data.method;
            document.getElementById('version').value = data.version;
            document.getElementById('businessSystem').value = data.businessSystem;
            document.getElementById('categoryId').value = data.categoryId;
            document.getElementById('description').value = data.description;
            document.getElementById('requestExample').value = data.requestExample;
            document.getElementById('responseExample').value = data.responseExample;
            document.getElementById('status').value = data.status;
            document.getElementById('isDeprecated').checked = data.isDeprecated;

            // 加载请求参数
            if (data.requestParameters) {
                loadParameters('request', data.requestParameters);
            }

            // 加载响应参数
            if (data.responseParameters) {
                loadParameters('response', data.responseParameters);
            }
        }
    }

    // 加载参数数据
    function loadParameters(type, parameters) {
        const containerId = type === 'request' ? 'requestParameters' : 'responseParameters';
        const container = document.getElementById(containerId);
        const className = type === 'request' ? 'request-param-row' : 'response-param-row';

        // 清除现有参数行（除了表头）
        const existingRows = container.querySelectorAll(`.${className}`);
        existingRows.forEach(row => row.remove());

        // 递归添加参数行
        parameters.forEach((param, index) => {
            const row = loadParameterRow(type, param, 0, container);
        });
    }

    // 递归加载参数行（包括子参数）
    function loadParameterRow(type, param, level, parentContainer, parentName = '') {
        const row = createParameterRow(type, level, parentName);
        const inputs = row.querySelectorAll(':scope > div > div input, :scope > div > div select');

        // 填充数据
        if (inputs.length >= 4) {
            inputs[0].value = param.name; // 参数名
            inputs[1].value = param.type; // 类型
            inputs[2].checked = param.required; // 必填
            inputs[3].value = param.description; // 描述

            // 触发类型变化事件以显示/隐藏子参数按钮
            handleTypeChange(inputs[1]);
        }

        parentContainer.appendChild(row);

        // 加载子参数
        if (param.children && param.children.length > 0) {
            const childrenContainer = row.querySelector('.children-container');
            param.children.forEach(childParam => {
                loadParameterRow(type, childParam, level + 1, childrenContainer, param.name);
            });
        }

        return row;
    }

    // 处理表单提交
    document.getElementById('apiForm').addEventListener('submit', function (e) {
        e.preventDefault();

        // 表单验证
        if (!validateForm()) {
            alert('请检查并修正表单中的错误');
            return;
        }

        // 获取表单数据
        const formData = new FormData(this);
        const apiData = {};
        for (let [key, value] of formData.entries()) {
            apiData[key] = value;
        }

        // 处理复选框
        apiData.isDeprecated = document.getElementById('isDeprecated').checked;

        // 收集请求参数
        apiData.requestParameters = collectParameters('request');

        // 收集响应参数
        apiData.responseParameters = collectParameters('response');

        // 模拟提交
        const submitButton = document.getElementById('submitButtonText');
        const submitButtonElement = submitButton.parentElement;
        const originalText = submitButton.textContent;

        // 禁用提交按钮并显示加载状态
        submitButtonElement.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';

        // 模拟API调用
        setTimeout(() => {
            const isCreate = originalText.includes('注册');
            const action = isCreate ? '注册' : '更新';

            // 显示成功消息
            showSuccessMessage(`API ${action}成功！接口 "${apiData.interfaceName}" 已${action}。`);

            // 重置按钮状态
            submitButtonElement.disabled = false;
            submitButton.textContent = originalText;

            // 关闭弹框
            closeApiModal();

            // 模拟刷新API列表
            if (isCreate) {
                addApiToList(apiData);
            }

            console.log('API数据：', apiData);
        }, 2000);
    });

    // 收集参数数据
    function collectParameters(type) {
        const containerId = type === 'request' ? 'requestParameters' : 'responseParameters';
        const container = document.getElementById(containerId);
        const className = type === 'request' ? 'request-param-row' : 'response-param-row';

        // 只收集顶级参数行
        const topLevelRows = container.querySelectorAll(`.${className}[data-level="0"]`);
        const parameters = [];

        topLevelRows.forEach(row => {
            const param = collectParameterData(row);
            if (param && param.name) {
                parameters.push(param);
            }
        });

        return parameters;
    }

    // 递归收集参数数据（包括子参数）
    function collectParameterData(row) {
        const inputs = row.querySelectorAll(':scope > div > div input, :scope > div > div select');
        if (inputs.length < 4) return null;

        const name = inputs[0].value.trim();
        const paramType = inputs[1].value;
        const required = inputs[2].checked;
        const description = inputs[3].value.trim();

        if (!name) return null;

        const param = {
            name: name,
            type: paramType,
            required: required,
            description: description
        };

        // 收集子参数
        const childrenContainer = row.querySelector('.children-container');
        if (childrenContainer && childrenContainer.children.length > 0) {
            param.children = [];
            Array.from(childrenContainer.children).forEach(childRow => {
                const childParam = collectParameterData(childRow);
                if (childParam && childParam.name) {
                    param.children.push(childParam);
                }
            });
        }

        return param;
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
        successDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            successDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(successDiv);
            }, 300);
        }, 3000);
    }

    // 模拟添加API到列表
    function addApiToList(apiData) {
        // 这里可以动态添加新的API到页面列表中
        console.log('添加新API到列表:', apiData);
    }

    // 点击模态框外部关闭
    document.getElementById('apiModal').addEventListener('click', function (e) {
        if (e.target === this) {
            closeApiModal();
        }
    });

    // 业务系统变化时更新分类选项
    document.getElementById('businessSystem').addEventListener('change', function () {
        const categorySelect = document.getElementById('categoryId');
        const selectedSystem = this.value;

        // 重置分类选择
        categorySelect.value = '';

        // 根据业务系统显示对应的分类选项
        const optgroups = categorySelect.querySelectorAll('optgroup');
        optgroups.forEach(optgroup => {
            if (optgroup.label === selectedSystem || selectedSystem === '') {
                optgroup.style.display = 'block';
            } else {
                optgroup.style.display = 'none';
            }
        });
    });

    // 实时表单验证
    function setupFormValidation() {
        const form = document.getElementById('apiForm');
        const inputs = form.querySelectorAll('input[required], select[required]');

        inputs.forEach(input => {
            input.addEventListener('blur', function () {
                validateField(this);
            });

            input.addEventListener('input', function () {
                // 清除之前的错误状态
                this.classList.remove('border-red-500');
                const errorMsg = this.parentNode.querySelector('.error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }


            });
        });
    }

    // 验证单个字段
    function validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // 移除之前的错误状态
        field.classList.remove('border-red-500');
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // 必填字段验证
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = '此字段为必填项';
        }

        // 特殊字段验证
        if (field.id === 'interfacePath' && value) {
            if (!value.startsWith('/')) {
                isValid = false;
                errorMessage = '接口路径必须以 / 开头';
            } else if (!/^\/[a-zA-Z0-9\/_{}.-]*$/.test(value)) {
                isValid = false;
                errorMessage = '接口路径格式不正确';
            }
        }

        if (field.id === 'interfaceName' && value && value.length < 2) {
            isValid = false;
            errorMessage = '接口名称至少需要2个字符';
        }

        // 显示错误状态
        if (!isValid) {
            field.classList.add('border-red-500');
            const errorDiv = document.createElement('p');
            errorDiv.className = 'error-message text-xs text-red-500 mt-1';
            errorDiv.textContent = errorMessage;
            field.parentNode.appendChild(errorDiv);
        }

        return isValid;
    }

    // 表单提交时的完整验证
    function validateForm() {
        const form = document.getElementById('apiForm');
        const requiredFields = form.querySelectorAll('input[required], select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!validateField(field)) {
                isValid = false;
            }
        });

        return isValid;
    }

    // 键盘快捷键支持
    document.addEventListener('keydown', function (e) {
        // ESC键关闭弹框
        if (e.key === 'Escape') {
            const apiModal = document.getElementById('apiModal');
            const rateLimitModal = document.getElementById('rateLimitModal');

            if (!apiModal.classList.contains('hidden')) {
                closeApiModal();
            } else if (!rateLimitModal.classList.contains('hidden')) {
                closeRateLimitModal();
            }
        }

        // Ctrl+Enter 提交表单
        if (e.ctrlKey && e.key === 'Enter') {
            const apiModal = document.getElementById('apiModal');
            if (!apiModal.classList.contains('hidden')) {
                document.getElementById('apiForm').dispatchEvent(new Event('submit'));
            }
        }
    });

    // 自动保存草稿功能
    function setupAutoSave() {
        const form = document.getElementById('apiForm');
        const inputs = form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            input.addEventListener('input', function () {
                // 延迟保存，避免频繁操作
                clearTimeout(this.saveTimeout);
                this.saveTimeout = setTimeout(() => {
                    saveFormDraft();
                }, 1000);
            });
        });
    }


    // 关闭API弹框
    function closeApiModal() {
        console.log('closeApiModal called');
        try {
            const modal = document.getElementById('apiModal');
            const form = document.getElementById('apiForm');

            if (modal) {
                modal.classList.add('hidden');
                console.log('Modal hidden');
            }

            if (form) {
                form.reset();
                // 重置参数表格
                resetParameterTables();
                console.log('Form reset');
            }
        } catch (error) {
            console.error('Error in closeApiModal:', error);
        }
    }

    // 添加请求参数
    function addRequestParameter() {
        const container = document.getElementById('requestParameters');
        const newRow = createParameterRow('request');
        container.appendChild(newRow);
    }

    // 添加响应参数
    function addResponseParameter() {
        const container = document.getElementById('responseParameters');
        const newRow = createParameterRow('response');
        container.appendChild(newRow);
    }

    // 创建参数行
    function createParameterRow(type, level = 0, parentName = '') {
        const row = document.createElement('div');
        const prefix = type === 'request' ? 'req' : 'res';
        const className = type === 'request' ? 'request-param-row' : 'response-param-row';

        row.className = className;
        row.setAttribute('data-level', level);

        // 根据层级添加样式
        let levelClass = '';
        if (level > 0) {
            levelClass = `param-level-${Math.min(level, 3)}`;
        }

        row.innerHTML = `
                <div class="grid grid-cols-12 gap-2 items-center ${levelClass} ${level > 0 ? 'py-1 px-2 rounded' : ''}">
                    <div class="col-span-3">
                        <input type="text" name="${prefix}ParamName[]"
                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                               placeholder="${level > 0 ? '子参数名' : '参数名'}"
                               data-parent="${parentName}">
                    </div>
                    <div class="col-span-2">
                        <select name="${prefix}ParamType[]" onchange="handleTypeChange(this)"
                                class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                            <option value="string">String</option>
                            <option value="integer">Integer</option>
                            <option value="boolean">Boolean</option>
                            <option value="array">Array</option>
                            <option value="object">Object</option>
                        </select>
                    </div>
                    <div class="col-span-1">
                        <input type="checkbox" name="${prefix}ParamRequired[]"
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    </div>
                    <div class="col-span-4">
                        <input type="text" name="${prefix}ParamDesc[]"
                               class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500"
                               placeholder="参数描述">
                    </div>
                    <div class="col-span-2 flex space-x-1">
                        <button type="button" onclick="addChildParameter(this, '${type}')"
                                class="add-child-btn text-green-600 hover:text-green-500 text-sm"
                                title="添加子参数" style="display: none;">
                            <i class="fas fa-plus-circle"></i>
                        </button>
                        <button type="button" onclick="removeParameter(this)"
                                class="text-red-600 hover:text-red-500 text-sm" title="删除参数">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="children-container ml-6"></div>
            `;
        return row;
    }

    // 处理类型变化
    function handleTypeChange(selectElement) {
        const row = selectElement.closest('.request-param-row, .response-param-row');
        const addChildBtn = row.querySelector('.add-child-btn');
        const childrenContainer = row.querySelector('.children-container');

        if (selectElement.value === 'object' || selectElement.value === 'array') {
            // 显示添加子参数按钮
            addChildBtn.style.display = 'inline-block';
        } else {
            // 隐藏添加子参数按钮并清除子参数
            addChildBtn.style.display = 'none';
            if (childrenContainer) {
                childrenContainer.innerHTML = '';
            }
        }
    }

    // 添加子参数
    function addChildParameter(button, type) {
        const parentRow = button.closest('.request-param-row, .response-param-row');
        const childrenContainer = parentRow.querySelector('.children-container');
        const parentLevel = parseInt(parentRow.getAttribute('data-level')) || 0;
        const parentNameInput = parentRow.querySelector('input[name*="ParamName"]');
        const parentName = parentNameInput.value || 'parent';

        // 限制嵌套层级（最多3级）
        if (parentLevel >= 3) {
            alert('最多支持3级嵌套参数');
            return;
        }

        const childRow = createParameterRow(type, parentLevel + 1, parentName);
        childrenContainer.appendChild(childRow);

        // 自动聚焦到新添加的参数名输入框
        const nameInput = childRow.querySelector('input[name*="ParamName"]');
        if (nameInput) {
            nameInput.focus();
        }
    }

    // 删除参数行
    function removeParameter(button) {
        const row = button.closest('.request-param-row, .response-param-row');
        if (row) {
            // 确认删除（如果有子参数）
            const childrenContainer = row.querySelector('.children-container');
            const hasChildren = childrenContainer && childrenContainer.children.length > 0;

            if (hasChildren) {
                if (!confirm('删除此参数将同时删除所有子参数，确定要删除吗？')) {
                    return;
                }
            }

            row.remove();
        }
    }

    // 重置参数表格
    function resetParameterTables() {
        // 重置请求参数
        const requestContainer = document.getElementById('requestParameters');
        const requestRows = requestContainer.querySelectorAll('.request-param-row');
        requestRows.forEach((row, index) => {
            if (index === 0) {
                // 保留第一行但清空内容
                resetParameterRow(row);
            } else {
                // 删除其他行
                row.remove();
            }
        });

        // 重置响应参数
        const responseContainer = document.getElementById('responseParameters');
        const responseRows = responseContainer.querySelectorAll('.response-param-row');
        responseRows.forEach((row, index) => {
            if (index === 0) {
                // 保留第一行但清空内容
                resetParameterRow(row);
            } else {
                // 删除其他行
                row.remove();
            }
        });
    }

    // 重置单个参数行
    function resetParameterRow(row) {
        // 清空主要输入框
        const inputs = row.querySelectorAll(':scope > div > div input, :scope > div > div select');
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });

        // 清空子参数容器
        const childrenContainer = row.querySelector('.children-container');
        if (childrenContainer) {
            childrenContainer.innerHTML = '';
        }

        // 隐藏添加子参数按钮
        const addChildBtn = row.querySelector('.add-child-btn');
        if (addChildBtn) {
            addChildBtn.style.display = 'none';
        }
    }

    // ========================================
    // 用户管理相关函数
    // ========================================

    // 打开用户管理弹框
    function openUserModal(mode, userId = null) {
        const modal = document.getElementById('userModal');
        const title = document.getElementById('userModalTitle');
        const submitButton = document.getElementById('userSubmitButtonText');
        const form = document.getElementById('userForm');

        if (!modal || !title || !submitButton || !form) {
            console.error('用户弹框元素未找到');
            return;
        }

        // 重置表单
        form.reset();

        if (mode === 'create') {
            title.textContent = '新增企业用户';
            submitButton.textContent = '新增用户';
            // 设置默认值
            const userStatusElement = document.getElementById('userStatus');
            if (userStatusElement) {
                userStatusElement.value = '1';
            }
        } else if (mode === 'edit') {
            title.textContent = '编辑企业用户';
            submitButton.textContent = '更新用户';
            // 加载用户数据
            loadUserData(userId);
        }

        modal.classList.remove('hidden');

        // 设置焦点
        setTimeout(() => {
            const firstInput = modal.querySelector('input');
            if (firstInput) {
                firstInput.focus();
            }
        }, 200);
    }

    // 关闭用户弹框
    function closeUserModal() {
        console.log('closeUserModal called');
        try {
            const modal = document.getElementById('userModal');
            const form = document.getElementById('userForm');

            if (modal) {
                modal.classList.add('hidden');
            }

            if (form) {
                form.reset();
            }
        } catch (error) {
            console.error('Error in closeUserModal:', error);
        }
    }



    // 加载用户数据（编辑模式）
    function loadUserData(userId) {
        // 模拟用户数据
        const mockUserData = {
            'zhang': {
                username: 'zhangdev',
                realName: '张开发者',
                email: '<EMAIL>',
                phone: '13800138001',
                userType: 'enterprise',
                userStatus: '1',
                userRemark: '前端开发工程师',
                companyName: '科技有限公司',
                creditCode: '91110000123456789A',
                legalPerson: '张总',
                companyScale: 'medium',
                industry: 'technology',
                authStatus: 'approved',
                companyAddress: '北京市海淀区中关村科技园区101号'
            },
            'li': {
                username: 'liengineer',
                realName: '李工程师',
                email: '<EMAIL>',
                phone: '13800138002',
                userType: 'enterprise',
                userStatus: '1',
                userRemark: '后端开发工程师',
                companyName: '创新科技公司',
                creditCode: '91110000123456789B',
                legalPerson: '李总',
                companyScale: 'small',
                industry: 'technology',
                authStatus: 'approved',
                companyAddress: '上海市浦东新区张江高科技园区202号'
            },
            'wang': {
                username: 'wangenterprise',
                realName: '王企业',
                email: '<EMAIL>',
                phone: '13800138003',
                userType: 'enterprise',
                userStatus: '1',
                userRemark: '企业管理员',
                companyName: '智能制造有限公司',
                creditCode: '91110000123456789C',
                legalPerson: '王总',
                companyScale: 'large',
                industry: 'manufacturing',
                authStatus: 'approved',
                companyAddress: '深圳市南山区科技园区303号'
            }
        };

        const userData = mockUserData[userId];
        if (userData) {
            // 填充基本信息
            const usernameElement = document.getElementById('username');
            const realNameElement = document.getElementById('realName');
            const emailElement = document.getElementById('email');
            const phoneElement = document.getElementById('phone');
            const userStatusElement = document.getElementById('userStatus');
            const userRemarkElement = document.getElementById('userRemark');

            if (usernameElement) usernameElement.value = userData.username;
            if (realNameElement) realNameElement.value = userData.realName;
            if (emailElement) emailElement.value = userData.email;
            if (phoneElement) phoneElement.value = userData.phone || '';
            if (userStatusElement) userStatusElement.value = userData.userStatus;
            if (userRemarkElement) userRemarkElement.value = userData.userRemark || '';

            // 填充企业信息
            const companyNameElement = document.getElementById('companyName');
            const creditCodeElement = document.getElementById('creditCode');
            const legalPersonElement = document.getElementById('legalPerson');
            const companyScaleElement = document.getElementById('companyScale');
            const industryElement = document.getElementById('industry');
            const companyAddressElement = document.getElementById('companyAddress');

            if (companyNameElement) companyNameElement.value = userData.companyName || '';
            if (creditCodeElement) creditCodeElement.value = userData.creditCode || '';
            if (legalPersonElement) legalPersonElement.value = userData.legalPerson || '';
            if (companyScaleElement) companyScaleElement.value = userData.companyScale || '';
            if (industryElement) industryElement.value = userData.industry || '';
            if (companyAddressElement) companyAddressElement.value = userData.companyAddress || '';
        }
    }

    // 显示用户操作成功消息
    function showUserSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
        successDiv.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            successDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (document.body.contains(successDiv)) {
                    document.body.removeChild(successDiv);
                }
            }, 300);
        }, 3000);
    }

    // ========================================
    // 密钥管理相关函数
    // ========================================

    // 打开密钥管理弹窗
    function openKeyManagement(username) {
        console.log('openKeyManagement called for user:', username);

        // 设置当前用户
        const userElement = document.getElementById('keyUser');
        if (userElement) {
            const userNames = {
                'zhang': '张开发者',
                'li': '李工程师',
                'wang': '王企业'
            };
            userElement.textContent = userNames[username] || username;
        }

        // 显示密钥管理模态框
        const modal = document.getElementById('keyManagementModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    // 关闭密钥管理弹窗
    function closeKeyManagementModal() {
        const modal = document.getElementById('keyManagementModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }



    // 打开编辑密钥权限弹窗
    function openEditKeyPermission(accessKey) {
        console.log('openEditKeyPermission called for key:', accessKey);

        // 根据accessKey设置表单数据
        const accessKeyInput = document.getElementById('accessKey');
        const keyNameInput = document.getElementById('keyName');

        if (accessKeyInput) {
            accessKeyInput.value = accessKey;
        }

        if (keyNameInput) {
            if (accessKey === 'ak-1234567890abcdef') {
                keyNameInput.value = '默认密钥';
            } else {
                keyNameInput.value = '测试密钥';
            }
        }

        // 显示编辑权限模态框
        const modal = document.getElementById('editKeyPermissionModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    // 关闭编辑密钥权限弹窗
    function closeEditKeyPermissionModal() {
        const modal = document.getElementById('editKeyPermissionModal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // 保存密钥权限
    function saveKeyPermission() {
        // 获取表单数据
        const keyName = document.getElementById('keyName')?.value;
        const accessKey = document.getElementById('accessKey')?.value;
        const permissionType = document.querySelector('input[name="permission-type"]:checked')?.id;

        // 如果是部分权限，获取选中的API权限
        let selectedPermissions = [];
        if (permissionType === 'permission-partial') {
            const checkedPermissions = document.querySelectorAll('input[name="apiPermissions"]:checked');
            selectedPermissions = Array.from(checkedPermissions).map(checkbox => checkbox.value);
        }

        console.log('Saving key permission:', {
            keyName,
            accessKey,
            permissionType,
            selectedPermissions
        });

        alert('密钥权限保存成功！');
        closeEditKeyPermissionModal();
    }

    // ========================================
    // 密钥权限管理相关函数
    // ========================================

    // 系统级权限选择
    function selectSystemPermissions(system) {
        const systemPermissions = {
            'user': ['user-info', 'user-create', 'user-update', 'user-delete', 'user-list'],
            'order': ['order-query', 'order-create', 'order-update', 'order-cancel', 'order-list'],
            'inventory': ['inventory-query', 'inventory-update', 'inventory-reserve', 'inventory-release'],
            'payment': ['payment-create', 'payment-query', 'payment-refund', 'payment-callback'],
            'analytics': ['analytics-report', 'analytics-stats', 'analytics-export']
        };

        const permissions = systemPermissions[system] || [];
        permissions.forEach(permValue => {
            const checkbox = document.querySelector(`input[name="apiPermissions"][value="${permValue}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        updateSelectedPermissionCount();
    }

    function clearSystemPermissions(system) {
        const systemPermissions = {
            'user': ['user-info', 'user-create', 'user-update', 'user-delete', 'user-list'],
            'order': ['order-query', 'order-create', 'order-update', 'order-cancel', 'order-list'],
            'inventory': ['inventory-query', 'inventory-update', 'inventory-reserve', 'inventory-release'],
            'payment': ['payment-create', 'payment-query', 'payment-refund', 'payment-callback'],
            'analytics': ['analytics-report', 'analytics-stats', 'analytics-export']
        };

        const permissions = systemPermissions[system] || [];
        permissions.forEach(permValue => {
            const checkbox = document.querySelector(`input[name="apiPermissions"][value="${permValue}"]`);
            if (checkbox) {
                checkbox.checked = false;
            }
        });
        updateSelectedPermissionCount();
    }

    function selectAllPermissions() {
        const permissionCheckboxes = document.querySelectorAll('input[name="apiPermissions"]');
        permissionCheckboxes.forEach(checkbox => checkbox.checked = true);
        updateSelectedPermissionCount();
    }

    function clearAllPermissions() {
        const permissionCheckboxes = document.querySelectorAll('input[name="apiPermissions"]');
        permissionCheckboxes.forEach(checkbox => checkbox.checked = false);
        updateSelectedPermissionCount();
    }

    function updateSelectedPermissionCount() {
        const selectedPermissions = document.querySelectorAll('input[name="apiPermissions"]:checked');
        const countElement = document.getElementById('selectedPermissionCount');
        if (countElement) {
            countElement.textContent = selectedPermissions.length;
        }
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function () {
        console.log('API管理模块已加载');

        // 检查关键元素
        const modal = document.getElementById('apiModal');
        const form = document.getElementById('apiForm');

        if (!modal || !form) {
            console.error('API弹框元素未找到');
            return;
        }

        // 为默认参数行初始化类型变化监听
        const defaultRequestRow = document.querySelector('#requestParameters .request-param-row select[name*="ParamType"]');
        const defaultResponseRow = document.querySelector('#responseParameters .response-param-row select[name*="ParamType"]');

        if (defaultRequestRow) {
            handleTypeChange(defaultRequestRow);
        }
        if (defaultResponseRow) {
            handleTypeChange(defaultResponseRow);
        }

        console.log('API管理模块初始化完成');

        // 初始化用户管理功能
        const userModal = document.getElementById('userModal');
        const userForm = document.getElementById('userForm');

        if (userModal && userForm) {
            // 处理用户表单提交
            userForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // 获取表单数据
                const formData = new FormData(this);
                const userData = {};
                for (let [key, value] of formData.entries()) {
                    userData[key] = value;
                }

                // 验证必填字段
                const requiredFields = ['username', 'realName', 'email', 'companyName'];
                const missingFields = requiredFields.filter(field => !userData[field]);

                if (missingFields.length > 0) {
                    alert('请填写所有必填字段');
                    return;
                }

                // 验证邮箱格式
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(userData.email)) {
                    alert('请输入正确的邮箱格式');
                    return;
                }

                // 模拟提交
                const submitButton = document.getElementById('userSubmitButtonText');
                const submitButtonElement = submitButton.parentElement;
                const originalText = submitButton.textContent;

                // 禁用提交按钮并显示加载状态
                submitButtonElement.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';

                // 模拟API调用
                setTimeout(() => {
                    const isCreate = originalText.includes('新增');
                    const action = isCreate ? '新增' : '更新';

                    // 显示成功消息
                    showUserSuccessMessage(`用户${action}成功！用户 "${userData.realName}" 已${action}。`);

                    // 重置按钮状态
                    submitButtonElement.disabled = false;
                    submitButton.textContent = originalText;

                    // 关闭弹框
                    closeUserModal();

                    console.log('用户数据：', userData);
                }, 2000);
            });

            // 点击模态框外部关闭
            userModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeUserModal();
                }
            });

            console.log('用户管理模块初始化完成');
        }

        // 初始化密钥管理功能
        const keyManagementModal = document.getElementById('keyManagementModal');
        const editKeyPermissionModal = document.getElementById('editKeyPermissionModal');

        if (keyManagementModal) {
            // 点击模态框外部关闭
            keyManagementModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeKeyManagementModal();
                }
            });
        }

        if (editKeyPermissionModal) {
            // 点击模态框外部关闭
            editKeyPermissionModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditKeyPermissionModal();
                }
            });

            // 权限类型切换
            const permissionRadios = editKeyPermissionModal.querySelectorAll('input[name="permission-type"]');
            permissionRadios.forEach(radio => {
                radio.addEventListener('change', function(e) {
                    const apiPermissionSection = document.getElementById('apiPermissionSection');
                    if (apiPermissionSection) {
                        if (e.target.id === 'permission-all') {
                            apiPermissionSection.style.display = 'none';
                        } else {
                            apiPermissionSection.style.display = 'block';
                            // 初始化权限计数
                            updateSelectedPermissionCount();
                        }
                    }
                });
            });

            // 为API权限复选框添加变化监听器
            const permissionCheckboxes = document.querySelectorAll('input[name="apiPermissions"]');
            permissionCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedPermissionCount);
            });

            console.log('密钥管理模块初始化完成');
        }

        // 初始化API限流配置页面
        // 默认展开第一个业务系统
        const firstSystemGroup = document.querySelector('.business-system-group');
        if (firstSystemGroup) {
            const systemId = firstSystemGroup.dataset.system;
            toggleSystemGroup(systemId);
        }
    });
</script>
</body>
</html>
