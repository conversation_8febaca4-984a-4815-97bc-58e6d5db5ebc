<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar-scroll {
            max-height: calc(100vh - 120px);
            overflow-y: auto;
        }
        .api-card {
            transition: all 0.3s ease;
        }
        .api-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        .method-get { background: #10b981; }
        .method-post { background: #3b82f6; }
        .method-put { background: #f59e0b; }
        .method-delete { background: #ef4444; }
        .tab-active {
            background-color: #6366f1;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img class="h-8 w-8" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center" alt="Logo">
                    <span class="ml-3 text-xl font-bold text-gray-800">企业API开放平台</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" placeholder="搜索API..." 
                               class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">张开发者</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- 侧边栏 -->
        <div class="w-80 bg-white shadow-sm h-screen">
            <div class="p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">API分类</h2>
                <!-- 分类标签 -->
                <div class="flex mb-4 bg-gray-100 rounded-lg p-1">
                    <button id="business-tab" class="flex-1 py-2 px-4 text-sm font-medium rounded-md tab-active">
                        按业务系统
                    </button>
                    <button id="function-tab" class="flex-1 py-2 px-4 text-sm font-medium rounded-md">
                        按功能类型
                    </button>
                </div>
                <div class="sidebar-scroll">
                    <!-- 按业务系统分类 -->
                    <div id="business-category" class="category-section">
                        <!-- 用户管理系统 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-users text-blue-500 mr-2"></i>
                                    用户管理系统
                                </h3>
                                <span class="text-xs text-gray-500">12个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#user-info" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户信息查询</a>
                                <a href="#user-create" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户创建</a>
                                <a href="#user-update" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户信息更新</a>
                                <a href="#user-auth" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户认证</a>
                                <a href="#user-permission" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">权限管理</a>
                            </div>
                        </div>

                        <!-- 订单管理系统 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-shopping-cart text-green-500 mr-2"></i>
                                    订单管理系统
                                </h3>
                                <span class="text-xs text-gray-500">10个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#order-create" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单创建</a>
                                <a href="#order-query" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单查询</a>
                                <a href="#order-update" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单状态更新</a>
                                <a href="#order-cancel" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单取消</a>
                            </div>
                        </div>

                        <!-- 库存管理系统 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-warehouse text-purple-500 mr-2"></i>
                                    库存管理系统
                                </h3>
                                <span class="text-xs text-gray-500">8个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#inventory-query" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">库存查询</a>
                                <a href="#inventory-update" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">库存更新</a>
                                <a href="#inventory-in" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">入库操作</a>
                            </div>
                        </div>

                        <!-- 财务管理系统 -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-chart-bar text-red-500 mr-2"></i>
                                    财务管理系统
                                </h3>
                                <span class="text-xs text-gray-500">6个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#finance-report" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">财务报表</a>
                                <a href="#cost-analysis" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">成本分析</a>
                                <a href="#budget-query" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">预算查询</a>
                            </div>
                        </div>
                    </div>

                    <!-- 按功能类型分类 -->
                    <div id="function-category" class="category-section hidden">
                        <!-- 查询类API -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-search text-blue-500 mr-2"></i>
                                    查询类API
                                </h3>
                                <span class="text-xs text-gray-500">8个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#user-info" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户信息查询</a>
                                <a href="#order-query" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单查询</a>
                                <a href="#inventory-query" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">库存查询</a>
                                <a href="#budget-query" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">预算查询</a>
                            </div>
                        </div>

                        <!-- 操作类API -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-edit text-green-500 mr-2"></i>
                                    操作类API
                                </h3>
                                <span class="text-xs text-gray-500">10个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#user-create" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户创建</a>
                                <a href="#user-update" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户信息更新</a>
                                <a href="#order-create" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单创建</a>
                                <a href="#order-update" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单状态更新</a>
                                <a href="#inventory-update" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">库存更新</a>
                                <a href="#inventory-in" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">入库操作</a>
                            </div>
                        </div>

                        <!-- 管理类API -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-cogs text-purple-500 mr-2"></i>
                                    管理类API
                                </h3>
                                <span class="text-xs text-gray-500">6个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#user-auth" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">用户认证</a>
                                <a href="#user-permission" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">权限管理</a>
                                <a href="#order-cancel" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">订单取消</a>
                                <a href="#finance-report" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">财务报表</a>
                                <a href="#cost-analysis" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">成本分析</a>
                            </div>
                        </div>

                        <!-- AI服务类API -->
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-sm font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-robot text-red-500 mr-2"></i>
                                    AI服务类API
                                </h3>
                                <span class="text-xs text-gray-500">2个API</span>
                            </div>
                            <div class="space-y-2 ml-6">
                                <a href="#sentiment-analysis" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">情感分析</a>
                                <a href="#image-recognition" class="block text-sm text-gray-600 hover:text-indigo-600 py-1">图像识别</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="flex-1 p-8">
            <!-- 页面标题 -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">API文档</h1>
                <p class="mt-2 text-gray-600">完整的API接口文档，包含详细的参数说明和代码示例</p>
            </div>

            <!-- 快速开始 -->
            <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">快速开始</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-key text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-sm font-medium text-gray-900 mb-2">1. 获取API密钥</h3>
                        <p class="text-xs text-gray-600">在个人中心获取API密钥</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-code text-green-600 text-xl"></i>
                        </div>
                        <h3 class="text-sm font-medium text-gray-900 mb-2">2. 集成SDK</h3>
                        <p class="text-xs text-gray-600">下载对应语言的SDK</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-rocket text-purple-600 text-xl"></i>
                        </div>
                        <h3 class="text-sm font-medium text-gray-900 mb-2">3. 开始调用</h3>
                        <p class="text-xs text-gray-600">发送第一个API请求</p>
                    </div>
                </div>
            </div>

            <!-- API列表 -->
            <div class="space-y-8">
                <!-- 用户信息查询API -->
                <div id="user-info" class="bg-white rounded-lg shadow-sm api-card">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <span class="method-get text-white text-xs font-medium px-2 py-1 rounded mr-3">GET</span>
                                <h3 class="text-lg font-semibold text-gray-900">用户信息查询</h3>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">稳定</span>
                            </div>
                        </div>

                        <p class="text-gray-600 mb-4">根据用户ID查询用户的基本信息，包括姓名、邮箱、部门等</p>

                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <code class="text-sm text-gray-800">https://api.company.com/v1/user/info/{userId}</code>
                        </div>

                        <!-- 参数说明 -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">请求参数</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">参数名</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">必填</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">说明</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-2 text-sm font-medium text-gray-900">userId</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">string</td>
                                            <td class="px-4 py-2 text-sm text-red-600">是</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">用户唯一标识符</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 代码示例 -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">代码示例</h4>
                            <div class="bg-gray-900 rounded-lg p-4">
                                <pre><code class="language-javascript">// JavaScript示例
const response = await fetch('https://api.company.com/v1/user/info/12345', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
console.log(result);</code></pre>
                            </div>
                        </div>

                        <!-- 响应示例 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-3">响应示例</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "success",
  "data": {
    "userId": "12345",
    "name": "张三",
    "email": "<EMAIL>",
    "department": "技术部",
    "position": "前端工程师",
    "phone": "13800138000"
  }
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 情感分析API -->
                <div id="sentiment-analysis" class="bg-white rounded-lg shadow-sm api-card">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <span class="method-post text-white text-xs font-medium px-2 py-1 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">情感分析</h3>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">稳定</span>
                                <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-flask mr-1"></i>在线测试
                                </button>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 mb-4">对文本进行情感分析，识别正面、负面或中性情感，并提供置信度分数</p>
                        
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <code class="text-sm text-gray-800">https://api.ai-platform.com/v1/nlp/sentiment</code>
                        </div>

                        <!-- 参数说明 -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">请求参数</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">参数名</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">必填</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">说明</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-2 text-sm font-medium text-gray-900">text</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">string</td>
                                            <td class="px-4 py-2 text-sm text-red-600">是</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">待分析的文本内容，最大长度5000字符</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm font-medium text-gray-900">language</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">string</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">否</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">文本语言，支持zh-CN、en-US，默认自动检测</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 代码示例 -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">代码示例</h4>
                            <div class="bg-gray-900 rounded-lg p-4">
                                <pre><code class="language-javascript">// JavaScript示例
const response = await fetch('https://api.ai-platform.com/v1/nlp/sentiment', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '这个产品真的很棒，我非常喜欢！',
    language: 'zh-CN'
  })
});

const result = await response.json();
console.log(result);</code></pre>
                            </div>
                        </div>

                        <!-- 响应示例 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-3">响应示例</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "success",
  "data": {
    "sentiment": "positive",
    "confidence": 0.95,
    "scores": {
      "positive": 0.95,
      "negative": 0.03,
      "neutral": 0.02
    }
  }
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图像识别API -->
                <div id="image-recognition" class="bg-white rounded-lg shadow-sm api-card">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <span class="method-post text-white text-xs font-medium px-2 py-1 rounded mr-3">POST</span>
                                <h3 class="text-lg font-semibold text-gray-900">图像识别</h3>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">稳定</span>
                                <button class="text-indigo-600 hover:text-indigo-500 text-sm">
                                    <i class="fas fa-flask mr-1"></i>在线测试
                                </button>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 mb-4">识别图像中的物体、场景和内容，返回详细的识别结果和置信度</p>
                        
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <code class="text-sm text-gray-800">https://api.ai-platform.com/v1/vision/recognition</code>
                        </div>

                        <!-- 参数说明 -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">请求参数</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">参数名</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">必填</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">说明</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-2 text-sm font-medium text-gray-900">image</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">file/base64</td>
                                            <td class="px-4 py-2 text-sm text-red-600">是</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">图像文件或base64编码，支持jpg、png格式，最大10MB</td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-2 text-sm font-medium text-gray-900">max_results</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">integer</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">否</td>
                                            <td class="px-4 py-2 text-sm text-gray-500">返回结果的最大数量，默认10，最大100</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 代码示例 -->
                        <div class="mb-6">
                            <h4 class="text-md font-medium text-gray-900 mb-3">代码示例</h4>
                            <div class="bg-gray-900 rounded-lg p-4">
                                <pre><code class="language-python"># Python示例
import requests
import base64

# 读取图像文件
with open('image.jpg', 'rb') as f:
    image_data = base64.b64encode(f.read()).decode()

response = requests.post(
    'https://api.ai-platform.com/v1/vision/recognition',
    headers={
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    },
    json={
        'image': image_data,
        'max_results': 10
    }
)

result = response.json()
print(result)</code></pre>
                            </div>
                        </div>

                        <!-- 响应示例 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-3">响应示例</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <pre><code class="language-json">{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "label": "猫",
        "confidence": 0.98,
        "category": "动物"
      },
      {
        "label": "宠物",
        "confidence": 0.85,
        "category": "概念"
      }
    ]
  }
}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SDK下载 -->
            <div class="bg-white rounded-lg p-6 shadow-sm mt-8">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">SDK下载</h2>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50">
                        <i class="fab fa-js-square text-yellow-500 text-2xl mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">JavaScript</div>
                            <div class="text-sm text-gray-500">v1.2.0</div>
                        </div>
                    </a>
                    <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50">
                        <i class="fab fa-python text-blue-500 text-2xl mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">Python</div>
                            <div class="text-sm text-gray-500">v1.2.0</div>
                        </div>
                    </a>
                    <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50">
                        <i class="fab fa-java text-red-500 text-2xl mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">Java</div>
                            <div class="text-sm text-gray-500">v1.2.0</div>
                        </div>
                    </a>
                    <a href="#" class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-indigo-300 hover:bg-indigo-50">
                        <i class="fab fa-php text-purple-500 text-2xl mr-3"></i>
                        <div>
                            <div class="font-medium text-gray-900">PHP</div>
                            <div class="text-sm text-gray-500">v1.2.0</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换分类标签
        document.getElementById('business-tab').addEventListener('click', function() {
            document.getElementById('business-tab').classList.add('tab-active');
            document.getElementById('function-tab').classList.remove('tab-active');
            document.getElementById('business-category').classList.remove('hidden');
            document.getElementById('function-category').classList.add('hidden');
        });

        document.getElementById('function-tab').addEventListener('click', function() {
            document.getElementById('function-tab').classList.add('tab-active');
            document.getElementById('business-tab').classList.remove('tab-active');
            document.getElementById('function-category').classList.remove('hidden');
            document.getElementById('business-category').classList.add('hidden');
        });
    </script>
</body>
</html>