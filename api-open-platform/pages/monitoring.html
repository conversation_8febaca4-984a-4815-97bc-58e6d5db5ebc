<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监控告警 - 企业API开放平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .alert-high { border-left: 4px solid #ef4444; background: #fef2f2; }
        .alert-medium { border-left: 4px solid #f59e0b; background: #fffbeb; }
        .alert-low { border-left: 4px solid #10b981; background: #f0fdf4; }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <img class="h-8 w-8" src="https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=32&h=32&fit=crop&crop=center" alt="Logo">
                    <span class="ml-3 text-xl font-bold text-gray-800">企业API开放平台</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button class="p-2 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400"></span>
                        </button>
                    </div>
                    <div class="flex items-center space-x-3">
                        <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" alt="用户头像">
                        <span class="text-sm font-medium text-gray-700">张开发者</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">监控告警</h1>
            <p class="mt-2 text-gray-600">实时监控API性能指标，配置智能告警规则</p>
        </div>

        <!-- 系统状态概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!--<div class="bg-white rounded-lg p-6 shadow-sm metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-heartbeat text-green-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">系统状态</p>
                        <p class="text-2xl font-bold text-green-600">正常</p>
                        <p class="text-sm text-gray-500">99.9% 可用性</p>
                    </div>
                </div>
            </div>-->

            <div class="bg-white rounded-lg p-6 shadow-sm metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-blue-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">平均响应时间</p>
                        <p class="text-2xl font-bold text-gray-900">245ms</p>
                        <!--<p class="text-sm text-green-600">
                            <i class="fas fa-arrow-down mr-1"></i>-15ms
                        </p>-->
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">QPS</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                        <!--<p class="text-sm text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>+8.2%
                        </p>-->
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-sm metric-card">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">错误率</p>
                        <p class="text-2xl font-bold text-gray-900">0.2%</p>
                        <!--<p class="text-sm text-green-600">
                            <i class="fas fa-arrow-down mr-1"></i>-0.1%
                        </p>-->
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时监控图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 响应时间趋势 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">响应时间趋势</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded-full">实时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">1小时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">24小时</button>
                    </div>
                </div>
                <canvas id="responseTimeChart" width="400" height="200"></canvas>
            </div>

            <!-- QPS监控 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">QPS监控</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded-full">实时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">1小时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">24小时</button>
                    </div>
                </div>
                <canvas id="qpsChart" width="400" height="200"></canvas>
            </div>

            <!-- 错误率监控 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">错误率监控</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded-full">实时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">1小时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">24小时</button>
                    </div>
                </div>
                <canvas id="errorRateChart" width="400" height="200"></canvas>
            </div>

            <!-- 限流触发监控 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">限流触发监控</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs font-medium text-indigo-600 bg-indigo-100 rounded-full">实时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">1小时</button>
                        <button class="px-3 py-1 text-xs font-medium text-gray-500 hover:text-gray-700">24小时</button>
                    </div>
                </div>
                <canvas id="rateLimitChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 告警规则和最近告警 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 告警规则 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">告警规则</h3>
                    <button onclick="openAlertRuleModal('create')"
                            class="gradient-bg text-white px-4 py-2 rounded-lg font-medium hover:opacity-90">
                        <i class="fas fa-plus mr-2"></i>新建规则
                    </button>
                </div>

                <div class="space-y-4">
                    <!-- 告警规则1 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-md font-medium text-gray-900">响应时间告警</h4>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">启用</span>
                                <div class="relative">
                                    <button onclick="toggleRuleMenu('rule1')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div id="rule1-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                        <div class="py-1">
                                            <button onclick="openAlertRuleModal('edit', 'rule1')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-edit mr-2"></i>编辑规则
                                            </button>
                                            <button onclick="toggleRuleStatus('rule1')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-pause mr-2"></i>禁用规则
                                            </button>
                                            <button onclick="deleteRule('rule1')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                                <i class="fas fa-trash mr-2"></i>删除规则
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">当平均响应时间超过500ms时触发告警</p>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>阈值: > 500ms</span>
                            <span>时间窗口: 5分钟</span>
                            <span>告警级别: 中</span>
                        </div>
                    </div>

                    <!-- 告警规则2 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-md font-medium text-gray-900">错误率告警</h4>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">启用</span>
                                <div class="relative">
                                    <button onclick="toggleRuleMenu('rule2')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div id="rule2-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                        <div class="py-1">
                                            <button onclick="openAlertRuleModal('edit', 'rule2')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-edit mr-2"></i>编辑规则
                                            </button>
                                            <button onclick="toggleRuleStatus('rule2')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-pause mr-2"></i>禁用规则
                                            </button>
                                            <button onclick="deleteRule('rule2')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                                <i class="fas fa-trash mr-2"></i>删除规则
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">当错误率超过5%时触发告警</p>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>阈值: > 5%</span>
                            <span>时间窗口: 3分钟</span>
                            <span>告警级别: 高</span>
                        </div>
                    </div>

                    <!-- 告警规则3 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-md font-medium text-gray-900">QPS异常告警</h4>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-600 bg-gray-100 px-2 py-1 rounded-full">禁用</span>
                                <div class="relative">
                                    <button onclick="toggleRuleMenu('rule3')" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <div id="rule3-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border">
                                        <div class="py-1">
                                            <button onclick="openAlertRuleModal('edit', 'rule3')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-edit mr-2"></i>编辑规则
                                            </button>
                                            <button onclick="toggleRuleStatus('rule3')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-play mr-2"></i>启用规则
                                            </button>
                                            <button onclick="deleteRule('rule3')"
                                                    class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                                <i class="fas fa-trash mr-2"></i>删除规则
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">当QPS超过1000时触发告警</p>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>阈值: > 1000</span>
                            <span>时间窗口: 1分钟</span>
                            <span>告警级别: 中</span>
                        </div>
                    </div>

                    <!-- 告警规则4 -->
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-md font-medium text-gray-900">限流频率告警</h4>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">启用</span>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-3">当限流触发频率过高时告警</p>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>阈值: > 100次/小时</span>
                            <span>时间窗口: 10分钟</span>
                            <span>告警级别: 低</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近告警 -->
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">最近告警</h3>
                    <a href="#" class="text-sm text-indigo-600 hover:text-indigo-500">查看全部</a>
                </div>

                <div class="space-y-4">
                    <!-- 告警记录1 -->
                    <div class="alert-medium rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-orange-500 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-gray-900">响应时间异常</h4>
                                    <p class="text-xs text-gray-600 mt-1">平均响应时间达到650ms，超过阈值500ms</p>
                                    <p class="text-xs text-gray-500 mt-2">2024-02-20 14:25:30</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full">中级</span>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 告警记录2 -->
                    <div class="alert-low rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-info-circle text-green-500 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-gray-900">限流触发频率告警</h4>
                                    <p class="text-xs text-gray-600 mt-1">过去1小时限流触发120次，超过阈值100次</p>
                                    <p class="text-xs text-gray-500 mt-2">2024-02-20 13:45:15</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">低级</span>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 告警记录3 -->
                    <div class="alert-high rounded-lg p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-500 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-gray-900">错误率异常</h4>
                                    <p class="text-xs text-gray-600 mt-1">过去3分钟错误率达到8.5%，超过阈值5%</p>
                                    <p class="text-xs text-gray-500 mt-2">2024-02-20 12:30:45</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-red-600 bg-red-100 px-2 py-1 rounded-full">高级</span>
                                <button class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 告警记录4 -->
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-check-circle text-gray-400 text-lg"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-gray-500">响应时间恢复正常</h4>
                                    <p class="text-xs text-gray-500 mt-1">平均响应时间已恢复到正常水平</p>
                                    <p class="text-xs text-gray-400 mt-2">2024-02-20 11:15:20</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded-full">已恢复</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通知设置 -->
        <div class="bg-white rounded-lg p-6 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">通知设置</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- 邮件通知 -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-blue-500 text-xl mr-3"></i>
                            <h4 class="text-md font-medium text-gray-900">邮件通知</h4>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 text-sm text-gray-700">高级告警</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" checked class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 text-sm text-gray-700">中级告警</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 text-sm text-gray-700">低级告警</label>
                        </div>
                    </div>
                    <div class="mt-3">
                        <input type="email" value="<EMAIL>"
                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>

                    <!-- 邮件通知高级配置 -->
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">重试次数</label>
                                <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                    <option value="1">1次</option>
                                    <option value="2">2次</option>
                                    <option value="3" selected>3次</option>
                                    <option value="5">5次</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">重试间隔(秒)</label>
                                <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                    <option value="60">60秒</option>
                                    <option value="180">3分钟</option>
                                    <option value="300" selected>5分钟</option>
                                    <option value="600">10分钟</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 短信通知 -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-sms text-green-500 text-xl mr-3"></i>
                            <h4 class="text-md font-medium text-gray-900">短信通知</h4>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 text-sm text-gray-700">高级告警</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 text-sm text-gray-700">中级告警</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label class="ml-2 text-sm text-gray-700">低级告警</label>
                        </div>
                    </div>
                    <div class="mt-3">
                        <input type="tel" placeholder="138****8888"
                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>

                    <!-- 短信通知高级配置 -->
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">重试次数</label>
                                <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                    <option value="1">1次</option>
                                    <option value="2">2次</option>
                                    <option value="3" selected>3次</option>
                                    <option value="5">5次</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">重试间隔(秒)</label>
                                <select class="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-indigo-500">
                                    <option value="60">60秒</option>
                                    <option value="180">3分钟</option>
                                    <option value="300" selected>5分钟</option>
                                    <option value="600">10分钟</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 免打扰设置 -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-moon text-purple-500 text-xl mr-3"></i>
                            <h4 class="text-md font-medium text-gray-900">免打扰设置</h4>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" class="sr-only peer">
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                        </label>
                    </div>

                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">免打扰时间段</label>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">开始时间</label>
                                    <input type="time" value="22:00"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">结束时间</label>
                                    <input type="time" value="08:00"
                                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                </div>
                            </div>
                        </div>

                        <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                            <i class="fas fa-info-circle mr-1"></i>
                            免打扰期间，只有紧急级别的告警会发送通知
                        </div>
                    </div>
                </div>


            </div>

            <div class="mt-6 flex justify-end">
                <button class="gradient-bg text-white px-6 py-2 rounded-lg font-medium hover:opacity-90">
                    保存设置
                </button>
            </div>
        </div>
    </div>

    <!-- 告警规则配置弹窗 -->
    <div id="alertRuleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 id="alertRuleModalTitle" class="text-lg font-semibold text-gray-900">新建告警规则</h3>
                        <button onclick="closeAlertRuleModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <form id="alertRuleForm" class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="form-section">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div class="lg:col-span-2">
                                    <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>基本信息
                                    </h4>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">规则名称 <span class="text-red-500">*</span></label>
                                    <input type="text" id="ruleName" name="ruleName" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                           placeholder="请输入告警规则名称">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">监控指标 <span class="text-red-500">*</span></label>
                                    <select id="metricType" name="metricType" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <option value="">请选择监控指标</option>
                                        <option value="response_time">响应时间</option>
                                        <option value="error_rate">错误率</option>
                                        <option value="qps">QPS</option>
                                        <option value="cpu_usage">CPU使用率</option>
                                        <option value="memory_usage">内存使用率</option>
                                        <option value="disk_usage">磁盘使用率</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">比较操作符 <span class="text-red-500">*</span></label>
                                    <select id="operator" name="operator" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <option value="">请选择比较操作符</option>
                                        <option value="gt">大于 (>)</option>
                                        <option value="gte">大于等于 (>=)</option>
                                        <option value="lt">小于 (<)</option>
                                        <option value="lte">小于等于 (<=)</option>
                                        <option value="eq">等于 (=)</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">阈值 <span class="text-red-500">*</span></label>
                                    <div class="relative">
                                        <input type="number" id="threshold" name="threshold" required step="0.01"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                               placeholder="请输入阈值">
                                        <div id="thresholdUnit" class="absolute inset-y-0 right-0 flex items-center pr-3 text-sm text-gray-500 bg-gray-100 rounded-r-lg px-2">
                                            ms
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">时间窗口 <span class="text-red-500">*</span></label>
                                    <select id="timeWindow" name="timeWindow" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <option value="">请选择时间窗口</option>
                                        <option value="1">1分钟</option>
                                        <option value="3">3分钟</option>
                                        <option value="5">5分钟</option>
                                        <option value="10">10分钟</option>
                                        <option value="15">15分钟</option>
                                        <option value="30">30分钟</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">告警级别 <span class="text-red-500">*</span></label>
                                    <select id="alertLevel" name="alertLevel" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                        <option value="">请选择告警级别</option>
                                        <option value="low">低</option>
                                        <option value="medium">中</option>
                                        <option value="high">高</option>
                                        <option value="critical">紧急</option>
                                    </select>
                                </div>

                                <div class="lg:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">规则描述</label>
                                    <textarea id="ruleDescription" name="ruleDescription" rows="3"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                              placeholder="请输入告警规则的详细描述"></textarea>
                                </div>
                            </div>
                        </div>



                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            <button type="button" onclick="closeAlertRuleModal()"
                                    class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                                取消
                            </button>
                            <button type="submit"
                                    class="gradient-bg text-white px-6 py-2 rounded-lg font-medium hover:opacity-90">
                                <span id="alertRuleSubmitButtonText">创建规则</span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 响应时间趋势图
        const responseTimeCtx = document.getElementById('responseTimeChart').getContext('2d');
        new Chart(responseTimeCtx, {
            type: 'line',
            data: {
                labels: ['14:20', '14:22', '14:24', '14:26', '14:28', '14:30'],
                datasets: [{
                    label: '响应时间(ms)',
                    data: [220, 245, 280, 650, 420, 245],
                    borderColor: 'rgb(99, 102, 241)',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '响应时间 (ms)'
                        }
                    }
                }
            }
        });

        // QPS监控图
        const qpsCtx = document.getElementById('qpsChart').getContext('2d');
        new Chart(qpsCtx, {
            type: 'line',
            data: {
                labels: ['14:20', '14:22', '14:24', '14:26', '14:28', '14:30'],
                datasets: [{
                    label: 'QPS',
                    data: [120, 145, 168, 156, 189, 156],
                    borderColor: 'rgb(34, 197, 94)',
                    backgroundColor: 'rgba(34, 197, 94, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'QPS'
                        }
                    }
                }
            }
        });

        // 错误率监控图
        const errorRateCtx = document.getElementById('errorRateChart').getContext('2d');
        new Chart(errorRateCtx, {
            type: 'line',
            data: {
                labels: ['14:20', '14:22', '14:24', '14:26', '14:28', '14:30'],
                datasets: [{
                    label: '错误率(%)',
                    data: [0.1, 0.2, 0.15, 8.5, 2.1, 0.2],
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '错误率 (%)'
                        }
                    }
                }
            }
        });

        // 限流触发监控图
        const rateLimitCtx = document.getElementById('rateLimitChart').getContext('2d');
        new Chart(rateLimitCtx, {
            type: 'bar',
            data: {
                labels: ['14:20', '14:22', '14:24', '14:26', '14:28', '14:30'],
                datasets: [{
                    label: '限流次数',
                    data: [5, 8, 12, 25, 18, 10],
                    backgroundColor: 'rgba(251, 191, 36, 0.8)',
                    borderColor: 'rgb(251, 191, 36)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '限流次数'
                        }
                    }
                }
            }
        });

        // ========================================
        // 告警规则管理相关函数
        // ========================================

        // 打开告警规则配置弹窗
        function openAlertRuleModal(mode, ruleId = null) {
            console.log('openAlertRuleModal called with mode:', mode, 'ruleId:', ruleId);

            const modal = document.getElementById('alertRuleModal');
            const title = document.getElementById('alertRuleModalTitle');
            const submitButton = document.getElementById('alertRuleSubmitButtonText');
            const form = document.getElementById('alertRuleForm');
            const metricTypeSelect = document.getElementById('metricType');

            if (!modal || !title || !submitButton || !form) {
                console.error('告警规则弹框元素未找到');
                return;
            }

            // 重置表单
            form.reset();

            if (mode === 'create') {
                title.textContent = '新建告警规则';
                submitButton.textContent = '创建规则';
            } else if (mode === 'edit') {
                title.textContent = '编辑告警规则';
                submitButton.textContent = '更新规则';
                // 加载规则数据
                loadAlertRuleData(ruleId);
            }

            // 设置阈值单位
            if (metricTypeSelect) {
                // 延迟设置单位，确保表单重置完成
                setTimeout(() => {
                    setThresholdUnit();
                }, 0);
            }

            modal.classList.remove('hidden');

            // 设置焦点
            setTimeout(() => {
                const firstInput = modal.querySelector('input');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 200);
        }

        // 加载告警规则数据（编辑模式）
        function loadAlertRuleData(ruleId) {
            // 模拟告警规则数据
            const mockRuleData = {
                'rule1': {
                    ruleName: '响应时间告警',
                    metricType: 'response_time',
                    operator: 'gt',
                    threshold: 500,
                    timeWindow: '5',
                    alertLevel: 'medium',
                    ruleDescription: '当平均响应时间超过500ms时触发告警'
                },
                'rule2': {
                    ruleName: '错误率告警',
                    metricType: 'error_rate',
                    operator: 'gt',
                    threshold: 5,
                    timeWindow: '3',
                    alertLevel: 'high',
                    ruleDescription: '当错误率超过5%时触发告警'
                },
                'rule3': {
                    ruleName: 'QPS异常告警',
                    metricType: 'qps',
                    operator: 'gt',
                    threshold: 1000,
                    timeWindow: '1',
                    alertLevel: 'medium',
                    ruleDescription: '当QPS超过1000时触发告警'
                }
            };

            const ruleData = mockRuleData[ruleId];
            if (ruleData) {
                // 填充基本信息
                const ruleNameElement = document.getElementById('ruleName');
                const metricTypeElement = document.getElementById('metricType');
                const operatorElement = document.getElementById('operator');
                const thresholdElement = document.getElementById('threshold');
                const timeWindowElement = document.getElementById('timeWindow');
                const alertLevelElement = document.getElementById('alertLevel');
                const ruleDescriptionElement = document.getElementById('ruleDescription');

                if (ruleNameElement) ruleNameElement.value = ruleData.ruleName;
                if (metricTypeElement) metricTypeElement.value = ruleData.metricType;
                if (operatorElement) operatorElement.value = ruleData.operator;
                if (thresholdElement) thresholdElement.value = ruleData.threshold;
                if (timeWindowElement) timeWindowElement.value = ruleData.timeWindow;
                if (alertLevelElement) alertLevelElement.value = ruleData.alertLevel;
                if (ruleDescriptionElement) ruleDescriptionElement.value = ruleData.ruleDescription;
                
                // 更新阈值单位
                if (metricTypeElement) {
                    setTimeout(() => {
                        setThresholdUnit();
                    }, 0);
                }
            }
        }

        // 关闭告警规则弹框
        function closeAlertRuleModal() {
            const modal = document.getElementById('alertRuleModal');
            const form = document.getElementById('alertRuleForm');

            if (modal) {
                modal.classList.add('hidden');
            }

            if (form) {
                form.reset();
            }
        }

        // 切换规则菜单显示/隐藏
        function toggleRuleMenu(ruleId) {
            // 先隐藏所有菜单
            document.querySelectorAll('[id$="-menu"]').forEach(menu => {
                if (menu.id !== ruleId + '-menu') {
                    menu.classList.add('hidden');
                }
            });

            // 切换当前菜单
            const menu = document.getElementById(ruleId + '-menu');
            if (menu) {
                menu.classList.toggle('hidden');
            }
        }

        // 切换规则状态
        function toggleRuleStatus(ruleId) {
            // 隐藏菜单
            const menu = document.getElementById(ruleId + '-menu');
            if (menu) {
                menu.classList.add('hidden');
            }

            // 模拟切换状态
            alert(`规则 ${ruleId} 状态切换成功！`);

            // 这里可以添加实际的状态切换逻辑
            console.log('Toggle rule status for:', ruleId);
        }

        // 删除规则
        function deleteRule(ruleId) {
            // 隐藏菜单
            const menu = document.getElementById(ruleId + '-menu');
            if (menu) {
                menu.classList.add('hidden');
            }

            if (confirm('确定要删除这个告警规则吗？此操作不可恢复。')) {
                alert(`规则 ${ruleId} 删除成功！`);

                // 这里可以添加实际的删除逻辑
                console.log('Delete rule:', ruleId);
            }
        }

        // 显示成功消息
        function showAlertRuleSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-all duration-300';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(successDiv);

            // 3秒后自动移除
            setTimeout(() => {
                successDiv.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (document.body.contains(successDiv)) {
                        document.body.removeChild(successDiv);
                    }
                }, 300);
            }, 3000);
        }

        // 初始化告警规则管理功能
        document.addEventListener('DOMContentLoaded', function() {
            const alertRuleModal = document.getElementById('alertRuleModal');
            const alertRuleForm = document.getElementById('alertRuleForm');
            const metricTypeSelect = document.getElementById('metricType');
            const thresholdUnit = document.getElementById('thresholdUnit');

            // 根据监控指标设置阈值单位
            function setThresholdUnit() {
                if (!metricTypeSelect || !thresholdUnit) return;
                
                const metricType = metricTypeSelect.value;
                let unit = '';
                
                switch (metricType) {
                    case 'response_time':
                        unit = 'ms';
                        break;
                    case 'error_rate':
                        unit = '%';
                        break;
                    case 'qps':
                        unit = '次/秒';
                        break;
                    case 'cpu_usage':
                    case 'memory_usage':
                    case 'disk_usage':
                        unit = '%';
                        break;
                    default:
                        // 如果没有选择任何指标，显示默认单位
                        unit = metricType ? '' : 'ms';
                }
                
                thresholdUnit.textContent = unit;
                // 如果单位为空，隐藏单位显示元素
                thresholdUnit.style.display = unit ? 'flex' : 'none';
            }

            // 监听监控指标选择变化
            if (metricTypeSelect) {
                metricTypeSelect.addEventListener('change', setThresholdUnit);
            }

            if (alertRuleModal && alertRuleForm) {
                // 设置初始单位显示
                setThresholdUnit();

                // 处理告警规则表单提交
                alertRuleForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    // 获取表单数据
                    const formData = new FormData(this);
                    const ruleData = {};
                    for (let [key, value] of formData.entries()) {
                        ruleData[key] = value;
                    }

                    // 验证必填字段
                    const requiredFields = ['ruleName', 'metricType', 'operator', 'threshold', 'timeWindow', 'alertLevel'];
                    const missingFields = requiredFields.filter(field => !ruleData[field]);

                    if (missingFields.length > 0) {
                        alert('请填写所有必填字段');
                        return;
                    }

                    // 验证阈值
                    if (isNaN(ruleData.threshold) || ruleData.threshold <= 0) {
                        alert('请输入有效的阈值');
                        return;
                    }

                    // 模拟提交
                    const submitButton = document.getElementById('alertRuleSubmitButtonText');
                    const submitButtonElement = submitButton.parentElement;
                    const originalText = submitButton.textContent;

                    // 禁用提交按钮并显示加载状态
                    submitButtonElement.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';

                    // 模拟API调用
                    setTimeout(() => {
                        const isCreate = originalText.includes('创建');
                        const action = isCreate ? '创建' : '更新';

                        // 显示成功消息
                        showAlertRuleSuccessMessage(`告警规则${action}成功！规则 "${ruleData.ruleName}" 已${action}。`);

                        // 重置按钮状态
                        submitButtonElement.disabled = false;
                        submitButton.textContent = originalText;

                        // 关闭弹框
                        closeAlertRuleModal();

                        console.log('告警规则数据：', ruleData);
                    }, 2000);
                });

                // 点击模态框外部关闭
                alertRuleModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeAlertRuleModal();
                    }
                });
            }

            // 点击页面其他地方时隐藏所有菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.relative')) {
                    document.querySelectorAll('[id$="-menu"]').forEach(menu => {
                        menu.classList.add('hidden');
                    });
                }
            });
        });
    </script>
</body>
</html>
