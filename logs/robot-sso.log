2025-07-31 10:49:57.256 [main] INFO  [,] com.ok.robot.RobotSSOStartApplication - No active profile set, falling back to default profiles: default
2025-07-31 10:49:57.553 [main] INFO  [,] o.springframework.cloud.context.scope.GenericScope - BeanFactory id=6d712060-541e-3051-a8b1-3bb369eccbd4
2025-07-31 10:49:57.644 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.RegisterAuthApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.701 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.OrgApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.710 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.SystemParameterApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.719 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.RoleApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.726 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.DataPermissionApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.733 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.ResourcesApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.739 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.CompanyApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.745 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.EmployeeApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.752 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.UserApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.758 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.DataPoolApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.763 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.saas.SystemApiSaaSClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.768 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.CompanyApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.776 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.SystemParameterApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.782 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.ResourcesApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.792 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.OrgApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.800 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.RoleApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.806 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.SystemApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.815 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.CompanyAndUserApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.821 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.EmployeeApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.827 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.UserApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.831 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.DataPermissionApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.837 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.admin.sdk.client.DataPoolApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.843 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.sso.sdk.client.AuthenApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:57.850 [main] INFO  [,] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.zjkj.saas.sso.sdk.client.SmsApiClient' of type [org.springframework.cloud.openfeign.FeignClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-31 10:49:58.060 [main] INFO  [,] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 10001 (http)
2025-07-31 10:49:58.067 [main] INFO  [,] org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-10001"]
2025-07-31 10:49:58.067 [main] INFO  [,] org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-31 10:49:58.067 [main] INFO  [,] org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.37]
2025-07-31 10:49:58.120 [main] INFO  [,] o.a.c.c.C.[Tomcat].[localhost].[/robot-sso] - Initializing Spring embedded WebApplicationContext
2025-07-31 10:49:58.120 [main] INFO  [,] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 856 ms
2025-07-31 10:49:58.203 [main] INFO  [,] com.ok.robot.filter.TraceIdFilter - TraceIdFilter initialized
2025-07-31 10:49:58.275 [main] DEBUG [,] o.s.c.s.instrument.async.SleuthContextListener - Context refreshed or closed [org.springframework.context.event.ContextRefreshedEvent[source=FeignContext-authenApiClient, started on Thu Jul 31 10:49:58 CST 2025, parent: org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@89c65d5]]
2025-07-31 10:49:58.320 [main] WARN  [,] com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-31 10:49:58.320 [main] INFO  [,] com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-31 10:49:58.322 [main] WARN  [,] com.netflix.config.sources.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-07-31 10:49:58.322 [main] INFO  [,] com.netflix.config.sources.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-31 10:49:58.410 [main] INFO  [,] o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-07-31 10:49:58.546 [main] DEBUG [,] o.s.c.s.i.hystrix.SleuthHystrixConcurrencyStrategy - Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@5a13f1f7],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@5dae5a70],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@7686f701],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@47c96f2c],]
2025-07-31 10:49:58.546 [main] DEBUG [,] o.s.c.s.i.hystrix.SleuthHystrixConcurrencyStrategy - Registering Sleuth Hystrix Concurrency Strategy.
2025-07-31 10:49:58.572 [main] DEBUG [,] o.s.c.s.i.rxjava.SleuthRxJavaSchedulersHook - Current RxJava plugins configuration is [schedulersHook [rx.plugins.RxJavaSchedulersHook@77ff14ce],errorHandler [rx.plugins.RxJavaPlugins$1@733fae8],observableExecutionHook [rx.plugins.RxJavaObservableExecutionHookDefault@5a8d676e],]
2025-07-31 10:49:58.572 [main] DEBUG [,] o.s.c.s.i.rxjava.SleuthRxJavaSchedulersHook - Registering Sleuth RxJava Schedulers Hook.
2025-07-31 10:49:58.621 [main] INFO  [,] org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-10001"]
2025-07-31 10:49:58.637 [main] INFO  [,] o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 10001 (http) with context path '/robot-sso'
2025-07-31 10:49:58.645 [main] DEBUG [,] o.s.c.s.instrument.async.SleuthContextListener - Context refreshed or closed [org.springframework.context.event.ContextRefreshedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@89c65d5, started on Thu Jul 31 10:49:57 CST 2025, parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@3eeb318f]]
2025-07-31 10:49:58.647 [main] INFO  [,] com.ok.robot.RobotSSOStartApplication - Started RobotSSOStartApplication in 1.837 seconds (JVM running for 2.295)
2025-07-31 11:42:45.636 [SpringContextShutdownHook] DEBUG [,] o.s.c.s.instrument.async.SleuthContextListener - Context refreshed or closed [org.springframework.context.event.ContextClosedEvent[source=org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@89c65d5, started on Thu Jul 31 10:49:57 CST 2025, parent: org.springframework.context.annotation.AnnotationConfigApplicationContext@3eeb318f]]
2025-07-31 11:42:46.925 [SpringContextShutdownHook] INFO  [,] com.ok.robot.filter.TraceIdFilter - TraceIdFilter destroyed
2025-07-31 11:42:46.938 [SpringContextShutdownHook] INFO  [,] o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
