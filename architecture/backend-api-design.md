# 企业API开放平台 - 后端接口设计

## 1. 认证服务接口 (auth-service)

### 1.1 用户认证相关
```
POST /api/v1/auth/login                    # 用户登录
POST /api/v1/auth/logout                   # 用户登出
POST /api/v1/auth/register                 # 用户注册
POST /api/v1/auth/register/verify          # 邮箱验证
POST /api/v1/auth/password/reset           # 密码重置
POST /api/v1/auth/password/change          # 修改密码
POST /api/v1/auth/token/refresh            # Token刷新
GET  /api/v1/auth/user/info                # 获取当前用户信息
```

### 1.2 API密钥管理
```
GET    /api/v1/auth/keys                   # 获取用户密钥列表
POST   /api/v1/auth/keys                   # 创建新密钥
PUT    /api/v1/auth/keys/{keyId}           # 更新密钥信息
DELETE /api/v1/auth/keys/{keyId}           # 删除密钥
POST   /api/v1/auth/keys/{keyId}/regenerate # 重新生成密钥
PUT    /api/v1/auth/keys/{keyId}/status    # 启用/禁用密钥
```

### 1.3 权限验证
```
POST /api/v1/auth/validate                 # API密钥验证
POST /api/v1/auth/permission/check         # 权限检查
GET  /api/v1/auth/permission/list          # 获取用户权限列表
```

## 2. 用户服务接口 (user-service)

### 2.1 用户信息管理
```
GET  /api/v1/user/profile                  # 获取用户详细信息
PUT  /api/v1/user/profile                  # 更新用户信息
GET  /api/v1/user/quota                    # 获取用户配额信息
PUT  /api/v1/user/quota                    # 更新用户配额
GET  /api/v1/user/usage/statistics         # 获取用户使用统计
```

### 2.2 企业认证
```
POST /api/v1/user/enterprise/apply         # 申请企业认证
GET  /api/v1/user/enterprise/status        # 获取企业认证状态
PUT  /api/v1/user/enterprise/info          # 更新企业信息
```

## 3. API管理服务接口 (api-service)

### 3.1 API文档相关
```
GET  /api/v1/api/docs                      # 获取API文档列表
GET  /api/v1/api/docs/{apiId}              # 获取API详细文档
GET  /api/v1/api/categories                # 获取API分类列表
GET  /api/v1/api/business-systems          # 获取业务系统列表
```

### 3.2 API管理（管理员）
```
GET    /api/v1/admin/apis                  # 获取API列表
POST   /api/v1/admin/apis                  # 创建新API
PUT    /api/v1/admin/apis/{apiId}          # 更新API信息
DELETE /api/v1/admin/apis/{apiId}          # 删除API
PUT    /api/v1/admin/apis/{apiId}/status   # 更新API状态
```

### 3.3 API分类管理
```
GET    /api/v1/admin/categories            # 获取分类列表
POST   /api/v1/admin/categories            # 创建分类
PUT    /api/v1/admin/categories/{id}       # 更新分类
DELETE /api/v1/admin/categories/{id}       # 删除分类
```

### 3.4 限流规则管理
```
GET  /api/v1/admin/rate-limits             # 获取限流规则列表
POST /api/v1/admin/rate-limits             # 创建限流规则
PUT  /api/v1/admin/rate-limits/{id}        # 更新限流规则
GET  /api/v1/admin/rate-limits/apis        # 获取API限流配置
PUT  /api/v1/admin/rate-limits/apis/{apiId} # 更新API限流配置
```

## 4. 日志服务接口 (log-service)

### 4.1 调用日志查询
```
GET  /api/v1/logs/calls                    # 获取调用日志列表
GET  /api/v1/logs/calls/{logId}            # 获取日志详情
GET  /api/v1/logs/calls/export             # 导出日志数据
POST /api/v1/logs/calls/search             # 高级搜索日志
```

### 4.2 统计分析
```
GET  /api/v1/logs/statistics/overview      # 获取概览统计
GET  /api/v1/logs/statistics/trend         # 获取趋势数据
GET  /api/v1/logs/statistics/distribution  # 获取分布统计
GET  /api/v1/logs/statistics/top-apis      # 获取热门API统计
GET  /api/v1/logs/statistics/error-analysis # 获取错误分析
```

### 4.3 日志记录（内部接口）
```
POST /api/v1/logs/record                   # 记录API调用日志
POST /api/v1/logs/batch-record             # 批量记录日志
```

## 5. 监控服务接口 (monitor-service)

### 5.1 监控数据
```
GET  /api/v1/monitor/overview              # 获取监控概览
GET  /api/v1/monitor/metrics/realtime      # 获取实时监控数据
GET  /api/v1/monitor/metrics/history       # 获取历史监控数据
GET  /api/v1/monitor/metrics/trend         # 获取趋势数据
```

### 5.2 告警规则管理
```
GET    /api/v1/monitor/alert-rules         # 获取告警规则列表
POST   /api/v1/monitor/alert-rules         # 创建告警规则
PUT    /api/v1/monitor/alert-rules/{id}    # 更新告警规则
DELETE /api/v1/monitor/alert-rules/{id}    # 删除告警规则
PUT    /api/v1/monitor/alert-rules/{id}/status # 启用/禁用告警规则
```

### 5.3 告警记录
```
GET  /api/v1/monitor/alerts                # 获取告警记录
GET  /api/v1/monitor/alerts/{id}           # 获取告警详情
PUT  /api/v1/monitor/alerts/{id}/status    # 更新告警状态
```

### 5.4 通知配置
```
GET  /api/v1/monitor/notification-config   # 获取通知配置
PUT  /api/v1/monitor/notification-config   # 更新通知配置
POST /api/v1/monitor/notification/test     # 测试通知发送
```

## 6. 通知服务接口 (notify-service)

### 6.1 通知发送
```
POST /api/v1/notify/email                  # 发送邮件通知
POST /api/v1/notify/sms                    # 发送短信通知
POST /api/v1/notify/webhook                # 发送Webhook通知
```

### 6.2 通知模板管理
```
GET    /api/v1/notify/templates            # 获取通知模板列表
POST   /api/v1/notify/templates            # 创建通知模板
PUT    /api/v1/notify/templates/{id}       # 更新通知模板
DELETE /api/v1/notify/templates/{id}       # 删除通知模板
```

### 6.3 通知记录
```
GET  /api/v1/notify/records                # 获取通知发送记录
GET  /api/v1/notify/records/{id}           # 获取通知详情
```

## 7. 控制台数据接口 (dashboard-service)

### 7.1 控制台概览
```
GET  /api/v1/dashboard/overview            # 获取控制台概览数据
GET  /api/v1/dashboard/statistics          # 获取统计数据
GET  /api/v1/dashboard/charts/call-trend   # 获取调用趋势图数据
GET  /api/v1/dashboard/charts/api-distribution # 获取API使用分布数据
GET  /api/v1/dashboard/charts/response-time # 获取响应时间分布数据
```

### 7.2 快捷操作
```
GET  /api/v1/dashboard/recent-apis         # 获取最近使用的API
GET  /api/v1/dashboard/quick-stats         # 获取快速统计数据
```

## 8. 系统管理接口 (admin-service)

### 8.1 用户管理
```
GET    /api/v1/admin/users                 # 获取用户列表
GET    /api/v1/admin/users/{userId}        # 获取用户详情
PUT    /api/v1/admin/users/{userId}        # 更新用户信息
PUT    /api/v1/admin/users/{userId}/status # 更新用户状态
GET    /api/v1/admin/users/{userId}/keys   # 获取用户密钥列表
POST   /api/v1/admin/users/{userId}/keys   # 为用户创建密钥
DELETE /api/v1/admin/users/{userId}/keys/{keyId} # 删除用户密钥
```

### 8.2 系统配置
```
GET  /api/v1/admin/config                  # 获取系统配置
PUT  /api/v1/admin/config                  # 更新系统配置
GET  /api/v1/admin/config/categories       # 获取配置分类
```

### 8.3 数据统计
```
GET  /api/v1/admin/statistics/users        # 用户统计数据
GET  /api/v1/admin/statistics/apis         # API统计数据
GET  /api/v1/admin/statistics/calls        # 调用统计数据
GET  /api/v1/admin/statistics/system       # 系统统计数据
```

## 9. 业务代理服务接口

### 9.1 用户管理系统代理 (user-proxy-service)
```
GET    /api/v1/proxy/user/info/{userId}    # 查询用户信息
POST   /api/v1/proxy/user/create           # 创建用户
PUT    /api/v1/proxy/user/update/{userId}  # 更新用户信息
DELETE /api/v1/proxy/user/delete/{userId}  # 删除用户
GET    /api/v1/proxy/user/list             # 用户列表查询
```

### 9.2 订单管理系统代理 (order-proxy-service)
```
GET  /api/v1/proxy/order/query/{orderId}   # 查询订单信息
POST /api/v1/proxy/order/create            # 创建订单
PUT  /api/v1/proxy/order/update/{orderId}  # 更新订单
POST /api/v1/proxy/order/cancel/{orderId}  # 取消订单
GET  /api/v1/proxy/order/list              # 订单列表查询
```

### 9.3 库存管理系统代理 (inventory-proxy-service)
```
GET  /api/v1/proxy/inventory/query/{productId} # 查询库存信息
PUT  /api/v1/proxy/inventory/update/{productId} # 更新库存
POST /api/v1/proxy/inventory/reserve       # 预留库存
POST /api/v1/proxy/inventory/release       # 释放库存
```

### 9.4 财务管理系统代理 (finance-proxy-service)
```
GET  /api/v1/proxy/finance/bill/query      # 查询账单信息
POST /api/v1/proxy/finance/payment/process # 处理支付
GET  /api/v1/proxy/finance/payment/query   # 查询支付状态
POST /api/v1/proxy/finance/refund          # 申请退款
```

### 9.5 AI服务代理 (ai-proxy-service)
```
POST /api/v1/proxy/ai/nlp/sentiment        # 文本情感分析
POST /api/v1/proxy/ai/vision/recognition   # 图像识别
POST /api/v1/proxy/ai/speech/recognition   # 语音识别
POST /api/v1/proxy/ai/translation          # 机器翻译
```

## 10. 公共接口

### 10.1 文件上传
```
POST /api/v1/common/upload                 # 文件上传
GET  /api/v1/common/download/{fileId}      # 文件下载
```

### 10.2 系统信息
```
GET  /api/v1/common/health                 # 健康检查
GET  /api/v1/common/version                # 版本信息
GET  /api/v1/common/time                   # 服务器时间
```

## 11. 接口设计规范

### 11.1 统一响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": **********000,
  "traceId": "trace_**********001"
}
```

### 11.2 分页响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 11.3 错误码定义
```
200  - 成功
400  - 请求参数错误
401  - 未授权/认证失败
403  - 权限不足
404  - 资源不存在
429  - 请求过于频繁
500  - 服务器内部错误
502  - 外部服务不可用
503  - 服务暂时不可用
```

## 12. 接口安全设计

### 12.1 API密钥认证
```
Headers:
X-Access-Key: your_access_key
X-Timestamp: **********
X-Signature: calculated_signature
```

### 12.2 签名算法
```
signature = HMAC_SHA256(
  HTTP_METHOD + "\n" +
  REQUEST_URI + "\n" +
  QUERY_STRING + "\n" +
  TIMESTAMP + "\n" +
  MD5(REQUEST_BODY),
  SECRET_KEY
)
```

### 12.3 限流策略
- **全局限流**：每个用户总体调用频率限制
- **API级限流**：单个API的调用频率限制
- **IP限流**：基于IP的访问频率限制

## 13. 接口版本管理

### 13.1 版本策略
- **URL版本控制**：/api/v1/、/api/v2/
- **向后兼容**：新版本保持向后兼容
- **废弃通知**：提前通知接口废弃计划

### 13.2 版本发布流程
1. **开发阶段**：/api/dev/
2. **测试阶段**：/api/beta/
3. **正式发布**：/api/v1/
4. **废弃标记**：/api/v1/ (deprecated)

## 14. 核心接口详细设计

### 14.1 用户登录接口
```
POST /api/v1/auth/login

请求参数:
{
  "email": "<EMAIL>",
  "password": "password123",
  "userType": "personal|enterprise"
}

响应数据:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "jwt_token_string",
    "refreshToken": "refresh_token_string",
    "user": {
      "id": 12345,
      "username": "zhangdev",
      "email": "<EMAIL>",
      "userType": "enterprise",
      "companyName": "科技有限公司",
      "status": "active"
    }
  }
}
```

### 14.2 API密钥创建接口
```
POST /api/v1/auth/keys

请求参数:
{
  "keyName": "生产环境密钥",
  "permissionType": "FULL|PARTIAL",
  "ipWhitelist": "***********/24,********",
  "expiredDays": 365,
  "description": "用于生产环境的API调用"
}

响应数据:
{
  "code": 200,
  "message": "密钥创建成功",
  "data": {
    "id": 1001,
    "keyName": "生产环境密钥",
    "accessKey": "ak_**********001",
    "secretKey": "sk_**********001_secret",
    "permissionType": "FULL",
    "status": "active",
    "createdAt": "2024-02-20T10:30:00Z",
    "expiredAt": "2025-02-20T10:30:00Z"
  }
}
```

### 14.3 调用日志查询接口
```
GET /api/v1/logs/calls

请求参数:
{
  "page": 1,
  "size": 20,
  "startTime": "2024-02-20T00:00:00Z",
  "endTime": "2024-02-20T23:59:59Z",
  "apiId": 123,
  "responseCode": 200,
  "accessKey": "ak_**********001",
  "requestIp": "*************"
}

响应数据:
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "list": [
      {
        "id": 1001,
        "traceId": "req_**********001",
        "apiName": "用户信息查询",
        "apiPath": "/v1/user/info",
        "requestMethod": "GET",
        "responseCode": 200,
        "responseTime": 145,
        "requestIp": "*************",
        "createdAt": "2024-02-20T14:30:25Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1247,
      "pages": 63
    }
  }
}
```

### 14.4 监控数据接口
```
GET /api/v1/monitor/overview

响应数据:
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "responseTime": {
      "current": 245,
      "trend": "****%"
    },
    "qps": {
      "current": 156,
      "trend": "+12.8%"
    },
    "errorRate": {
      "current": 0.8,
      "trend": "-2.1%"
    },
    "rateLimitTriggers": {
      "current": 23,
      "trend": "****%"
    }
  }
}
```

### 14.5 限流配置接口
```
PUT /api/v1/admin/rate-limits/apis/{apiId}

请求参数:
{
  "limitType": "MINUTE",
  "limitCount": 100
}

响应数据:
{
  "code": 200,
  "message": "配置保存成功",
  "data": {
    "apiId": 123,
    "apiName": "用户信息查询",
    "limitType": "MINUTE",
    "limitCount": 100,
    "updatedAt": "2024-02-20T15:30:00Z"
  }
}
```

## 15. 接口性能要求

### 15.1 响应时间要求
- **认证接口**：< 100ms
- **查询接口**：< 200ms
- **统计接口**：< 500ms
- **管理接口**：< 1s

### 15.2 并发要求
- **认证服务**：支持1000+ QPS
- **业务代理**：支持500+ QPS
- **日志服务**：支持异步处理，10000+ 日志/秒
- **监控服务**：支持实时数据更新

这个接口设计完全基于您的原型功能，涵盖了所有核心业务场景，支持高并发、高可用的企业级应用需求。
