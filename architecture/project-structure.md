# 企业API开放平台 - Java项目结构设计

## 1. Maven多模块项目结构

```
api-open-platform/
├── pom.xml                           # 父POM文件
├── README.md                         # 项目说明文档
├── docker-compose.yml               # 本地开发环境
├── kubernetes/                      # K8s部署文件
│   ├── namespace.yaml
│   ├── configmap.yaml
│   └── deployment/
├── docs/                           # 项目文档
│   ├── api/                        # API文档
│   ├── deployment/                 # 部署文档
│   └── development/                # 开发文档
│
├── platform-common/                # 公共模块
│   ├── platform-common-core/       # 核心工具类
│   ├── platform-common-web/        # Web通用配置
│   ├── platform-common-security/   # 安全相关
│   ├── platform-common-cache/      # 缓存工具
│   ├── platform-common-mq/         # 消息队列
│   └── platform-common-log/        # 日志工具
│
├── platform-gateway/               # API网关服务
│   ├── src/main/java/
│   ├── src/main/resources/
│   ├── src/test/java/
│   └── pom.xml
│
├── platform-auth/                  # 认证服务
│   ├── src/main/java/
│   │   └── com/company/platform/auth/
│   │       ├── AuthApplication.java
│   │       ├── controller/         # 控制器层
│   │       ├── service/            # 服务层
│   │       ├── repository/         # 数据访问层
│   │       ├── entity/             # 实体类
│   │       ├── dto/                # 数据传输对象
│   │       ├── config/             # 配置类
│   │       └── utils/              # 工具类
│   ├── src/main/resources/
│   │   ├── application.yml
│   │   ├── bootstrap.yml
│   │   └── mapper/                 # MyBatis映射文件
│   └── pom.xml
│
├── platform-user/                  # 用户服务
│   ├── src/main/java/
│   │   └── com/company/platform/user/
│   │       ├── UserApplication.java
│   │       ├── controller/
│   │       │   ├── UserController.java
│   │       │   └── UserKeyController.java
│   │       ├── service/
│   │       │   ├── UserService.java
│   │       │   ├── UserKeyService.java
│   │       │   └── impl/
│   │       ├── repository/
│   │       │   ├── UserRepository.java
│   │       │   └── UserKeyRepository.java
│   │       ├── entity/
│   │       │   ├── User.java
│   │       │   └── UserApiKey.java
│   │       └── dto/
│   │           ├── UserDTO.java
│   │           └── UserKeyDTO.java
│   └── pom.xml
│
├── platform-api/                   # API管理服务
│   ├── src/main/java/
│   │   └── com/company/platform/api/
│   │       ├── ApiApplication.java
│   │       ├── controller/
│   │       │   ├── ApiController.java
│   │       │   ├── ApiCategoryController.java
│   │       │   └── RateLimitController.java
│   │       ├── service/
│   │       │   ├── ApiService.java
│   │       │   ├── ApiCategoryService.java
│   │       │   └── RateLimitService.java
│   │       ├── repository/
│   │       ├── entity/
│   │       │   ├── Api.java
│   │       │   ├── ApiCategory.java
│   │       │   └── ApiRateLimit.java
│   │       └── dto/
│   └── pom.xml
│
├── platform-log/                   # 日志服务
│   ├── src/main/java/
│   │   └── com/company/platform/log/
│   │       ├── LogApplication.java
│   │       ├── controller/
│   │       │   └── LogController.java
│   │       ├── service/
│   │       │   ├── LogService.java
│   │       │   └── LogAnalysisService.java
│   │       ├── repository/
│   │       │   ├── LogRepository.java
│   │       │   └── LogElasticsearchRepository.java
│   │       ├── entity/
│   │       │   └── ApiCallLog.java
│   │       └── dto/
│   └── pom.xml
│
├── platform-monitor/               # 监控服务
│   ├── src/main/java/
│   │   └── com/company/platform/monitor/
│   │       ├── MonitorApplication.java
│   │       ├── controller/
│   │       │   ├── MonitorController.java
│   │       │   └── AlertController.java
│   │       ├── service/
│   │       │   ├── MonitorService.java
│   │       │   ├── AlertService.java
│   │       │   └── MetricsCollectorService.java
│   │       ├── repository/
│   │       ├── entity/
│   │       └── dto/
│   └── pom.xml
│
├── platform-notify/                # 通知服务
│   ├── src/main/java/
│   │   └── com/company/platform/notify/
│   │       ├── NotifyApplication.java
│   │       ├── controller/
│   │       ├── service/
│   │       │   ├── EmailNotifyService.java
│   │       │   ├── SmsNotifyService.java
│   │       │   └── WebhookNotifyService.java
│   │       ├── repository/
│   │       └── dto/
│   └── pom.xml
│
├── platform-proxy-user/            # 用户系统代理
├── platform-proxy-order/           # 订单系统代理
├── platform-proxy-inventory/       # 库存系统代理
├── platform-proxy-finance/         # 财务系统代理
├── platform-proxy-ai/              # AI服务代理
│
└── platform-admin/                 # 管理后台服务
    ├── src/main/java/
    │   └── com/company/platform/admin/
    │       ├── AdminApplication.java
    │       ├── controller/
    │       │   ├── UserManageController.java
    │       │   ├── ApiManageController.java
    │       │   └── SystemConfigController.java
    │       ├── service/
    │       └── dto/
    └── pom.xml
```

## 2. 核心模块详细设计

### 2.1 公共模块 (platform-common)

#### platform-common-core
```java
// 统一响应格式
public class ApiResponse<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
}

// 业务异常类
public class BusinessException extends RuntimeException {
    private Integer code;
    private String message;
}

// 常量定义
public class ApiConstants {
    public static final String ACCESS_KEY_HEADER = "X-Access-Key";
    public static final String SIGNATURE_HEADER = "X-Signature";
    public static final String TIMESTAMP_HEADER = "X-Timestamp";
}
```

#### platform-common-security
```java
// API签名工具
public class SignatureUtils {
    public static String generateSignature(String data, String secretKey);
    public static boolean verifySignature(String signature, String data, String secretKey);
}

// JWT工具
public class JwtUtils {
    public static String generateToken(UserDetails userDetails);
    public static Claims parseToken(String token);
}
```

### 2.2 网关服务 (platform-gateway)

#### 核心功能
- **路由配置**：动态路由到各个微服务
- **认证过滤器**：API密钥认证
- **限流过滤器**：请求限流控制
- **日志过滤器**：请求日志记录
- **跨域处理**：CORS配置

#### 关键配置
```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: auth-service
          uri: lb://platform-auth
          predicates:
            - Path=/api/v1/auth/**
        - id: user-service
          uri: lb://platform-user
          predicates:
            - Path=/api/v1/user/**
        - id: api-service
          uri: lb://platform-api
          predicates:
            - Path=/api/v1/api/**
```

### 2.3 认证服务 (platform-auth)

#### 核心实体
```java
@Entity
@Table(name = "user_api_keys")
public class UserApiKey {
    @Id
    private Long id;
    private Long userId;
    private String accessKey;
    private String secretKey;
    private String keyName;
    private String permissionType; // FULL, PARTIAL
    private String ipWhitelist;
    private Integer status;
    private LocalDateTime createdAt;
    private LocalDateTime expiredAt;
}
```

#### 核心服务
```java
@Service
public class AuthService {
    // API密钥验证
    public boolean validateApiKey(String accessKey, String signature, String data);
    
    // 权限检查
    public boolean checkApiPermission(String accessKey, String apiPath);
    
    // 生成密钥对
    public UserApiKey generateApiKey(Long userId, String keyName, String permissionType);
}
```

### 2.4 API管理服务 (platform-api)

#### 核心实体
```java
@Entity
@Table(name = "apis")
public class Api {
    @Id
    private Long id;
    private String apiName;
    private String apiPath;
    private String httpMethod;
    private String businessSystem; // 业务系统分类
    private Long categoryId;
    private String description;
    private String requestExample;
    private String responseExample;
    private Integer status;
}

@Entity
@Table(name = "api_rate_limits")
public class ApiRateLimit {
    @Id
    private Long id;
    private Long apiId;
    private String limitType; // SECOND, MINUTE, HOUR, DAY
    private Integer limitCount;
    private Integer priority;
}
```

## 3. 数据访问层设计

### 3.1 Repository层
```java
// 基础Repository接口
public interface BaseRepository<T, ID> extends JpaRepository<T, ID> {
    // 通用查询方法
}

// 用户Repository
public interface UserRepository extends BaseRepository<User, Long> {
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    List<User> findByUserTypeAndStatus(Integer userType, Integer status);
}

// API Repository
public interface ApiRepository extends BaseRepository<Api, Long> {
    List<Api> findByBusinessSystemAndStatus(String businessSystem, Integer status);
    List<Api> findByCategoryIdAndStatus(Long categoryId, Integer status);
}
```

### 3.2 缓存层设计
```java
@Service
public class CacheService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    // API信息缓存
    public void cacheApiInfo(String apiPath, Api api);
    public Api getApiFromCache(String apiPath);
    
    // 用户权限缓存
    public void cacheUserPermissions(String accessKey, Set<String> permissions);
    public Set<String> getUserPermissionsFromCache(String accessKey);
    
    // 限流计数
    public Long incrementRateLimit(String key, Duration expireTime);
    public Long getRateLimitCount(String key);
}
```

这个项目结构设计完全基于您的原型功能需求，采用标准的Spring Boot微服务架构，支持高并发、高可用的企业级应用开发。
