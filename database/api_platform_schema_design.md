# 企业API开放平台数据库设计文档

## 1. 设计原则

### 高可用设计原则
- **主从复制**：支持读写分离，查询操作使用从库
- **分区策略**：日志表按月分表，按日分区，便于数据管理和查询优化
- **软删除**：重要数据采用软删除机制，保证数据安全可恢复
- **数据冗余**：关键统计数据适度冗余，减少实时计算压力

### 高性能设计原则
- **索引优化**：基于实际查询场景设计复合索引
- **分表策略**：大数据量表采用分表设计
- **缓存友好**：数据结构设计便于Redis缓存
- **避免大事务**：表结构设计避免复杂关联和长事务

## 2. 核心表设计

### 2.1 用户管理模块

#### 用户基础信息表 (users)
```sql
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(64) NOT NULL COMMENT '用户名',
  `email` varchar(128) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `salt` varchar(32) NOT NULL COMMENT '密码盐值',
  `real_name` varchar(64) DEFAULT NULL COMMENT '真实姓名',
  `company_name` varchar(128) DEFAULT NULL COMMENT '企业名称',
  `user_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '用户类型:1个人,2企业',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1正常,2待审核',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  KEY `idx_phone` (`phone`),
  KEY `idx_status_type` (`status`, `user_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_company` (`company_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户基础信息表';
```

### 2.2 API管理模块

#### API分类表 (api_categories)
```sql
CREATE TABLE `api_categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_name` varchar(64) NOT NULL COMMENT '分类名称',
  `category_code` varchar(32) NOT NULL COMMENT '分类编码',
  `category_type` varchar(20) NOT NULL DEFAULT 'business' COMMENT '分类类型:business业务系统,function功能类型',
  `parent_id` bigint(20) unsigned DEFAULT '0' COMMENT '父分类ID',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `icon` varchar(64) DEFAULT NULL COMMENT '图标类名',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_code` (`category_code`),
  KEY `idx_type_parent` (`category_type`, `parent_id`),
  KEY `idx_parent_sort` (`parent_id`, `sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API分类表';
```

#### API接口定义表 (apis)
```sql
CREATE TABLE `apis` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `api_code` varchar(64) NOT NULL COMMENT 'API编码',
  `api_name` varchar(128) NOT NULL COMMENT 'API名称',
  `api_path` varchar(255) NOT NULL COMMENT 'API路径',
  `http_method` varchar(10) NOT NULL COMMENT 'HTTP方法',
  `category_id` bigint(20) unsigned NOT NULL COMMENT '分类ID',
  `description` text COMMENT 'API描述',
  `request_example` text COMMENT '请求示例',
  `response_example` text COMMENT '响应示例',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0下线,1上线,2维护',
  `version` varchar(16) NOT NULL DEFAULT 'v1' COMMENT 'API版本',
  `is_auth_required` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否需要认证',
  `default_rate_limit_per_minute` int(11) DEFAULT '100' COMMENT '默认每分钟限流次数',
  `default_rate_limit_per_hour` int(11) DEFAULT '1000' COMMENT '默认每小时限流次数',
  `avg_response_time` int(11) DEFAULT '0' COMMENT '平均响应时间(ms)',
  `today_call_count` int(11) DEFAULT '0' COMMENT '今日调用次数',
  `total_call_count` bigint(20) DEFAULT '0' COMMENT '总调用次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_code` (`api_code`),
  UNIQUE KEY `uk_path_method` (`api_path`, `http_method`),
  KEY `idx_category_status` (`category_id`, `status`),
  KEY `idx_status_version` (`status`, `version`),
  FOREIGN KEY (`category_id`) REFERENCES `api_categories` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API接口定义表';
```

### 2.3 密钥管理模块

#### 用户API密钥表 (user_api_keys)
```sql
CREATE TABLE `user_api_keys` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `key_name` varchar(64) NOT NULL COMMENT '密钥名称',
  `access_key` varchar(64) NOT NULL COMMENT '访问密钥',
  `secret_key` varchar(128) NOT NULL COMMENT '私钥',
  `key_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '密钥类型:1正式,2测试',
  `permission_type` tinyint(4) NOT NULL DEFAULT '2' COMMENT '权限类型:1全权限,2部分权限',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `allowed_ips` text COMMENT '允许的IP地址,逗号分隔',
  `daily_quota` int(11) DEFAULT '10000' COMMENT '每日配额',
  `monthly_quota` int(11) DEFAULT '300000' COMMENT '每月配额',
  `last_used_time` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_access_key` (`access_key`),
  KEY `idx_user_status` (`user_id`, `status`),
  KEY `idx_expire_time` (`expire_time`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户API密钥表';
```

#### 密钥权限表 (key_permissions)
```sql
CREATE TABLE `key_permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `key_id` bigint(20) unsigned NOT NULL COMMENT '密钥ID',
  `api_id` bigint(20) unsigned NOT NULL COMMENT 'API ID',
  `permission_level` tinyint(4) NOT NULL DEFAULT '1' COMMENT '权限级别:1只读,2读写,3管理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key_api` (`key_id`, `api_id`),
  KEY `idx_api_id` (`api_id`),
  FOREIGN KEY (`key_id`) REFERENCES `user_api_keys` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`api_id`) REFERENCES `apis` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='密钥权限表';
```

### 2.4 调用日志模块 (分表设计)

#### API调用日志表 (api_call_logs_YYYYMM)
```sql
CREATE TABLE `api_call_logs_202412` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `trace_id` varchar(64) NOT NULL COMMENT '链路追踪ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `api_id` bigint(20) unsigned NOT NULL COMMENT 'API ID',
  `access_key` varchar(64) NOT NULL COMMENT '访问密钥',
  `request_ip` varchar(45) NOT NULL COMMENT '请求IP',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_path` varchar(255) NOT NULL COMMENT '请求路径',
  `request_headers` text COMMENT '请求头信息',
  `request_params` text COMMENT '请求参数',
  `response_code` int(11) NOT NULL COMMENT '响应状态码',
  `response_time` int(11) NOT NULL COMMENT '响应时间(ms)',
  `response_size` int(11) DEFAULT '0' COMMENT '响应大小(bytes)',
  `response_body` text COMMENT '响应内容',
  `error_message` text COMMENT '错误信息',
  `user_agent` varchar(500) DEFAULT NULL COMMENT 'User Agent',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '调用时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_trace_id` (`trace_id`),
  KEY `idx_user_created` (`user_id`, `created_at`),
  KEY `idx_api_created` (`api_id`, `created_at`),
  KEY `idx_access_key` (`access_key`),
  KEY `idx_response_code` (`response_code`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_response_time` (`response_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API调用日志表-202412'
PARTITION BY RANGE (TO_DAYS(created_at)) (
  PARTITION p20241201 VALUES LESS THAN (TO_DAYS('2024-12-01')),
  PARTITION p20241202 VALUES LESS THAN (TO_DAYS('2024-12-02')),
  -- 按日分区，便于数据管理和查询优化
  PARTITION p20241231 VALUES LESS THAN (TO_DAYS('2025-01-01'))
);
```

### 2.5 统计分析模块

#### 用户调用统计表 (user_call_stats)
```sql
CREATE TABLE `user_call_stats` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `api_id` bigint(20) unsigned NOT NULL COMMENT 'API ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `call_count` int(11) NOT NULL DEFAULT '0' COMMENT '调用次数',
  `success_count` int(11) NOT NULL DEFAULT '0' COMMENT '成功次数',
  `error_count` int(11) NOT NULL DEFAULT '0' COMMENT '错误次数',
  `avg_response_time` decimal(10,2) DEFAULT '0.00' COMMENT '平均响应时间(ms)',
  `max_response_time` int(11) DEFAULT '0' COMMENT '最大响应时间(ms)',
  `min_response_time` int(11) DEFAULT '0' COMMENT '最小响应时间(ms)',
  `total_response_time` bigint(20) DEFAULT '0' COMMENT '总响应时间(ms)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_api_date` (`user_id`, `api_id`, `stat_date`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_user_date` (`user_id`, `stat_date`),
  KEY `idx_api_date` (`api_id`, `stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户调用统计表'
PARTITION BY RANGE (TO_DAYS(stat_date)) (
  PARTITION p202411 VALUES LESS THAN (TO_DAYS('2024-12-01')),
  PARTITION p202412 VALUES LESS THAN (TO_DAYS('2025-01-01')),
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01'))
);
```

#### 系统监控指标表 (system_metrics)
```sql
CREATE TABLE `system_metrics` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `metric_time` timestamp NOT NULL COMMENT '指标时间',
  `qps` int(11) DEFAULT '0' COMMENT '每秒查询数',
  `avg_response_time` decimal(10,2) DEFAULT '0.00' COMMENT '平均响应时间(ms)',
  `error_rate` decimal(5,4) DEFAULT '0.0000' COMMENT '错误率',
  `success_rate` decimal(5,4) DEFAULT '1.0000' COMMENT '成功率',
  `total_calls` int(11) DEFAULT '0' COMMENT '总调用次数',
  `success_calls` int(11) DEFAULT '0' COMMENT '成功调用次数',
  `error_calls` int(11) DEFAULT '0' COMMENT '错误调用次数',
  `rate_limit_hits` int(11) DEFAULT '0' COMMENT '限流触发次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_metric_time` (`metric_time`),
  KEY `idx_metric_time` (`metric_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控指标表'
PARTITION BY RANGE (TO_DAYS(metric_time)) (
  PARTITION p202411 VALUES LESS THAN (TO_DAYS('2024-12-01')),
  PARTITION p202412 VALUES LESS THAN (TO_DAYS('2025-01-01')),
  PARTITION p202501 VALUES LESS THAN (TO_DAYS('2025-02-01'))
);
```

### 2.6 限流规则模块

#### 限流规则表 (rate_limit_rules)
```sql
CREATE TABLE `rate_limit_rules` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型:global全局,user用户,api接口',
  `target_type` varchar(20) DEFAULT NULL COMMENT '目标类型:user_id,api_id,user_type',
  `target_value` varchar(255) DEFAULT NULL COMMENT '目标值',
  `limit_type` varchar(20) NOT NULL COMMENT '限制类型:second,minute,hour,day',
  `limit_count` int(11) NOT NULL COMMENT '限制次数',
  `time_window` int(11) NOT NULL COMMENT '时间窗口(秒)',
  `priority` int(11) DEFAULT '0' COMMENT '优先级,数字越小优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type_status` (`rule_type`, `status`),
  KEY `idx_target` (`target_type`, `target_value`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='限流规则表';
```

### 2.7 监控告警模块

#### 告警规则表 (alert_rules)
```sql
CREATE TABLE `alert_rules` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型:response_time,error_rate,qps,rate_limit_hits',
  `operator` varchar(10) NOT NULL COMMENT '操作符:gt,lt,eq,gte,lte',
  `threshold` decimal(10,2) NOT NULL COMMENT '阈值',
  `time_window` int(11) NOT NULL COMMENT '时间窗口(分钟)',
  `alert_level` varchar(20) NOT NULL COMMENT '告警级别:low,medium,high,critical',
  `notify_methods` varchar(100) DEFAULT NULL COMMENT '通知方式:email,sms,webhook',
  `notify_users` text COMMENT '通知用户邮箱,逗号分隔',
  `silence_time` int(11) DEFAULT '60' COMMENT '静默时间(分钟)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_metric_status` (`metric_type`, `status`),
  KEY `idx_alert_level` (`alert_level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警规则表';
```

#### 告警记录表 (alert_records)
```sql
CREATE TABLE `alert_records` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `rule_id` bigint(20) unsigned NOT NULL COMMENT '规则ID',
  `alert_title` varchar(255) NOT NULL COMMENT '告警标题',
  `alert_content` text NOT NULL COMMENT '告警内容',
  `alert_level` varchar(20) NOT NULL COMMENT '告警级别',
  `metric_value` decimal(10,2) NOT NULL COMMENT '指标值',
  `threshold_value` decimal(10,2) NOT NULL COMMENT '阈值',
  `alert_status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:1触发,2恢复,3已处理',
  `notify_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '通知状态:0未发送,1已发送,2发送失败',
  `recovery_time` timestamp NULL DEFAULT NULL COMMENT '恢复时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '触发时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_rule_created` (`rule_id`, `created_at`),
  KEY `idx_alert_status` (`alert_status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_alert_level` (`alert_level`),
  FOREIGN KEY (`rule_id`) REFERENCES `alert_rules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警记录表';
```

#### 告警通知配置表 (alert_notification_configs)
```sql
CREATE TABLE `alert_notification_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `notification_type` varchar(20) NOT NULL COMMENT '通知类型:email,sms,webhook',
  `is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用该通知方式',
  `high_level_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收高级告警',
  `medium_level_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收中级告警',
  `low_level_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否接收低级告警',
  `critical_level_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否接收紧急告警',
  `notification_target` varchar(255) NOT NULL COMMENT '通知目标:邮箱地址/手机号/webhook地址',
  `template_id` varchar(32) DEFAULT NULL COMMENT '通知模板ID',
  `retry_times` tinyint(4) DEFAULT '3' COMMENT '重试次数',
  `retry_interval` int(11) DEFAULT '300' COMMENT '重试间隔(秒)',
  `quiet_hours_start` time DEFAULT NULL COMMENT '免打扰开始时间',
  `quiet_hours_end` time DEFAULT NULL COMMENT '免打扰结束时间',
  `is_quiet_enabled` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否启用免打扰',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_type` (`user_id`, `notification_type`),
  KEY `idx_user_enabled` (`user_id`, `is_enabled`),
  KEY `idx_notification_type` (`notification_type`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='告警通知配置表';
```

#### 通知发送记录表 (notification_send_logs)
```sql
CREATE TABLE `notification_send_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `alert_record_id` bigint(20) unsigned NOT NULL COMMENT '告警记录ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `notification_type` varchar(20) NOT NULL COMMENT '通知类型:email,sms,webhook',
  `notification_target` varchar(255) NOT NULL COMMENT '通知目标',
  `alert_level` varchar(20) NOT NULL COMMENT '告警级别',
  `send_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '发送状态:0待发送,1发送中,2发送成功,3发送失败',
  `send_time` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `response_code` varchar(10) DEFAULT NULL COMMENT '响应码',
  `response_message` text COMMENT '响应消息',
  `retry_count` tinyint(4) DEFAULT '0' COMMENT '重试次数',
  `next_retry_time` timestamp NULL DEFAULT NULL COMMENT '下次重试时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_alert_record` (`alert_record_id`),
  KEY `idx_user_type` (`user_id`, `notification_type`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_next_retry` (`next_retry_time`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`alert_record_id`) REFERENCES `alert_records` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知发送记录表';
```

#### 通知模板表 (notification_templates)
```sql
CREATE TABLE `notification_templates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `template_code` varchar(32) NOT NULL COMMENT '模板编码',
  `template_name` varchar(128) NOT NULL COMMENT '模板名称',
  `notification_type` varchar(20) NOT NULL COMMENT '通知类型:email,sms,webhook',
  `alert_level` varchar(20) NOT NULL COMMENT '告警级别:low,medium,high,critical',
  `subject_template` varchar(255) DEFAULT NULL COMMENT '主题模板(邮件用)',
  `content_template` text NOT NULL COMMENT '内容模板',
  `variables` text COMMENT '模板变量说明(JSON格式)',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认模板',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_type_level` (`notification_type`, `alert_level`),
  KEY `idx_status_default` (`status`, `is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知模板表';
```

## 告警通知配置表设计说明

### 核心设计思路

1. **用户维度配置**：每个用户可以独立配置不同通知方式的偏好
2. **通知类型支持**：邮件、短信、Webhook三种主要通知方式
3. **告警级别控制**：支持按告警级别（高、中、低、紧急）精细化控制
4. **免打扰功能**：支持设置免打扰时间段
5. **发送记录追踪**：完整记录通知发送状态和结果
6. **模板化管理**：支持自定义通知模板

### 表结构特点

#### alert_notification_configs表
- **一用户一类型一记录**：通过`uk_user_type`唯一约束保证
- **级别开关**：四个布尔字段控制不同级别告警的接收
- **重试机制**：支持配置重试次数和间隔
- **免打扰**：支持设置免打扰时间段

#### notification_send_logs表
- **发送追踪**：记录每次通知的发送状态和结果
- **重试支持**：记录重试次数和下次重试时间
- **性能优化**：按时间分区，定期清理历史记录

#### notification_templates表
- **模板管理**：支持不同通知类型和告警级别的模板
- **变量支持**：模板支持变量替换
- **默认模板**：系统提供默认模板，用户可自定义

### 使用示例

```sql
-- 用户配置邮件通知
INSERT INTO alert_notification_configs (
  user_id, notification_type, is_enabled,
  high_level_enabled, medium_level_enabled, low_level_enabled,
  notification_target
) VALUES (
  12345, 'email', 1,
  1, 1, 0,
  '<EMAIL>'
);

-- 查询用户的通知配置
SELECT * FROM alert_notification_configs
WHERE user_id = 12345 AND is_enabled = 1;

-- 记录通知发送
INSERT INTO notification_send_logs (
  alert_record_id, user_id, notification_type,
  notification_target, alert_level, send_status
) VALUES (
  67890, 12345, 'email',
  '<EMAIL>', 'high', 1
);
```

这个设计完全基于您原型中的通知设置界面，支持灵活的通知配置和可靠的发送追踪机制。
```
```

### 2.8 系统配置模块

#### 系统配置表 (system_configs)
```sql
CREATE TABLE `system_configs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `config_key` varchar(64) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型:string,number,boolean,json',
  `config_group` varchar(32) NOT NULL COMMENT '配置分组:basic,security,rate_limit,notification',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加密存储',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';
```

## 3. 初始化数据

### 3.1 API分类初始化数据
```sql
INSERT INTO `api_categories` (`category_name`, `category_code`, `category_type`, `parent_id`, `sort_order`, `description`, `icon`) VALUES
('用户管理系统', 'user_management', 'business', 0, 1, '用户相关的API接口', 'fas fa-users'),
('订单管理系统', 'order_management', 'business', 0, 2, '订单相关的API接口', 'fas fa-shopping-cart'),
('库存管理系统', 'inventory_management', 'business', 0, 3, '库存相关的API接口', 'fas fa-warehouse'),
('财务管理系统', 'finance_management', 'business', 0, 4, '财务相关的API接口', 'fas fa-chart-bar'),
('查询类API', 'query_apis', 'function', 0, 1, '数据查询类接口', 'fas fa-search'),
('操作类API', 'operation_apis', 'function', 0, 2, '数据操作类接口', 'fas fa-edit'),
('管理类API', 'management_apis', 'function', 0, 3, '系统管理类接口', 'fas fa-cogs'),
('AI服务类API', 'ai_service_apis', 'function', 0, 4, 'AI智能服务接口', 'fas fa-robot');
```

### 3.2 系统配置初始化数据
```sql
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `config_group`, `description`) VALUES
('platform_name', '企业API开放平台', 'string', 'basic', '平台名称'),
('api_base_url', 'https://api.company.com', 'string', 'basic', 'API基础URL'),
('default_monthly_quota', '10000', 'number', 'basic', '默认月配额'),
('max_file_size_mb', '10', 'number', 'basic', '最大文件大小(MB)'),
('force_https', 'true', 'boolean', 'security', '强制HTTPS'),
('enable_ip_whitelist', 'false', 'boolean', 'security', '启用IP白名单'),
('api_key_auto_expire', 'true', 'boolean', 'security', 'API密钥自动过期'),
('global_rate_limit_per_minute', '1000', 'number', 'rate_limit', '全局每分钟限流'),
('personal_user_rate_limit_per_hour', '500', 'number', 'rate_limit', '个人用户每小时限流'),
('enterprise_user_rate_limit_per_hour', '2000', 'number', 'rate_limit', '企业用户每小时限流');
```

## 4. 性能优化策略

### 4.1 索引优化策略
- **主键优化**：使用自增ID作为主键，提高插入性能
- **复合索引**：基于实际查询场景设计复合索引，如`(user_id, created_at)`用于用户日志查询
- **覆盖索引**：关键查询使用覆盖索引，减少回表操作
- **索引维护**：定期分析索引使用情况，删除无用索引

### 4.2 分表分区策略
- **日志表分表**：按月分表，如`api_call_logs_202412`
- **日志表分区**：每个月表内按日分区，便于数据清理和查询优化
- **统计表分区**：按月分区，支持快速的时间范围查询
- **历史数据归档**：调用日志保留6个月，统计数据长期保存

### 4.3 读写分离策略
- **主库写入**：所有写操作使用主库
- **从库查询**：日志查询、统计分析使用从库
- **缓存策略**：热点数据使用Redis缓存，如用户信息、API配置
- **异步处理**：统计数据异步计算，减少实时查询压力

### 4.4 查询优化
- **分页优化**：大数据量分页使用游标分页而非OFFSET
- **统计预聚合**：关键统计指标预先计算存储
- **慢查询监控**：监控并优化慢查询
- **连接池管理**：合理配置数据库连接池

## 5. 高可用保障

### 5.1 数据备份策略
- **全量备份**：每日凌晨进行全量备份
- **增量备份**：每小时进行增量备份
- **binlog备份**：实时binlog备份，支持点对点恢复
- **跨机房备份**：异地备份保证数据安全

### 5.2 故障恢复机制
- **主从自动切换**：使用MHA或其他高可用方案
- **数据一致性检查**：定期检查主从数据一致性
- **快速恢复**：制定详细的故障恢复流程
- **演练机制**：定期进行故障恢复演练

### 5.3 监控告警体系
- **数据库性能监控**：CPU、内存、磁盘IO、连接数
- **慢查询监控**：监控执行时间超过阈值的查询
- **主从延迟监控**：监控主从复制延迟
- **空间使用监控**：监控表空间和磁盘使用情况

## 6. 扩展性考虑

### 6.1 水平扩展
- **分库分表**：支持按用户ID或时间维度分库分表
- **读写分离**：支持多个从库分担读压力
- **缓存层**：Redis集群支持数据缓存
- **中间件支持**：支持MyCAT、ShardingSphere等分库分表中间件

### 6.2 业务扩展
- **多版本API**：支持API版本管理
- **权限细化**：支持更细粒度的权限控制
- **多租户**：支持多租户数据隔离
- **国际化**：支持多语言和时区

## 7. 安全设计

### 7.1 数据安全
- **敏感数据加密**：密码、密钥等敏感信息加密存储
- **访问控制**：基于角色的访问控制(RBAC)
- **审计日志**：完整的操作审计日志
- **数据脱敏**：生产数据脱敏后用于测试环境

### 7.2 接口安全
- **API认证**：基于Access Key和Secret Key的认证机制
- **IP白名单**：支持IP访问控制
- **请求签名**：防止请求被篡改
- **HTTPS强制**：所有API请求强制使用HTTPS

## 8. 运维管理

### 8.1 数据清理策略
- **日志数据**：调用日志保留6个月，超期自动清理
- **告警记录**：告警记录保留1年
- **统计数据**：统计数据长期保存，按年归档
- **临时数据**：定期清理临时表和无用数据

### 8.2 性能调优
- **定期分析**：定期分析表结构和索引使用情况
- **参数调优**：根据业务特点调优MySQL参数
- **硬件升级**：根据性能监控数据制定硬件升级计划
- **容量规划**：基于业务增长预测进行容量规划

这个数据库设计完全基于您的原型功能进行设计，涵盖了原型中展示的所有核心功能模块，并从高可用和高性能角度进行了优化。
